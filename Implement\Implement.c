/************************************************************
 * ??????????????????????????? 
 * ?????Implement.c
 * ????: ?????
 * ??: ?????????????????
 * ???: YunXiang_TechShare  
 * Q Q: 2228398717
 * ???????????
************************************************************/

/************************* ???? *************************/

#include "Implement.h"

/************************* ???? *************************/

/************************ ???????? ************************/

uint16_t time_cnt=0;

/************************ ???????? ************************/

/************************************************************ 
 * ????:       show_address(u8 mode)
 * ???:       ??LCD??????????? 
 * ????:       1 ???DHCP?????????
  	           ???? ?????????
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void show_address(u8 mode)
{
	u8 buf[30];
	if(mode==1)
	{
		sprintf((char*)buf,"MAC    :%d.%d.%d.%d.%d.%d",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//???MAC???
		LCD_ShowString(30,130,210,16,16,buf); 
		sprintf((char*)buf,"DHCP IP:%d.%d.%d.%d",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//??????IP???
		LCD_ShowString(30,150,210,16,16,buf); 
		sprintf((char*)buf,"DHCP GW:%d.%d.%d.%d",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//?????????
		LCD_ShowString(30,170,210,16,16,buf); 
		sprintf((char*)buf,"DHCP IP:%d.%d.%d.%d",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//?????????????
		LCD_ShowString(30,190,210,16,16,buf); 
	}
	else 
	{
		sprintf((char*)buf,"MAC      :%d.%d.%d.%d.%d.%d",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//???MAC???
		LCD_ShowString(30,130,210,16,16,buf); 
		sprintf((char*)buf,"Static IP:%d.%d.%d.%d",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//??????IP???
		LCD_ShowString(30,150,210,16,16,buf); 
		sprintf((char*)buf,"Static GW:%d.%d.%d.%d",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//?????????
		LCD_ShowString(30,170,210,16,16,buf); 
		sprintf((char*)buf,"Static IP:%d.%d.%d.%d",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//?????????????
		LCD_ShowString(30,190,210,16,16,buf); 
	}	
}

/************************************************************ 
 * ????:       System_Init(void)
 * ???:       ???????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void System_Init(void)
{
    systick_config();     // ???????
	
	  nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);//????NVIC???????2:2????????????2??????????
	
	  USART0_Config();  // ????????
	  printf("\r\n\r\n");
	  printf("========================================\r\n");
	  printf("    GD32F470ZGT6 LWIP Network Demo\r\n");
	  printf("    Hardware: LingYun Tech Board\r\n");
	  printf("    Firmware: V1.0\r\n");
	  printf("    Build Time: %s %s\r\n", __DATE__, __TIME__);
	  printf("========================================\r\n\r\n");

	  printf("[SYSTEM] System initialization started...\r\n");
	  printf("[SYSTEM] SysTick timer configured\r\n");
	  printf("[SYSTEM] NVIC interrupt priority configured\r\n");
	  printf("[SYSTEM] USART0 serial port configured\r\n");

	  LED_Init();  			//LED?????
	  printf("[SYSTEM] LED initialized\r\n");

	  KEY_Init();  			//Key initialization
	  printf("[SYSTEM] Key initialized\r\n");

	 // LCD_Init(); 			//LCD initialization
	  printf("[SYSTEM] LCD display initialized\r\n");

	  my_mem_init(SRAMIN);		//Initialize internal memory
	  my_mem_init(SRAMCCM);	//Initialize CCM memory
	  printf("[SYSTEM] Memory management initialized\r\n");
	
		POINT_COLOR = RED; 		
		LCD_ShowString(30,30,200,16,16,"LYIT GD32F4");
		LCD_ShowString(30,50,200,16,16,"Ethernet lwIP Test");
		LCD_ShowString(30,70,200,16,16,"LYIT@GD32F470");
		LCD_ShowString(30,90,200,16,16,"2024/03/11"); 	
		Timer3_Init(9999,239); //100hz frequency
		printf("[SYSTEM] Timer3 initialized (100Hz)\r\n");
		printf("[SYSTEM] Basic hardware initialization completed\r\n\r\n");

		printf("[SYSTEM] Starting LWIP network stack initialization...\r\n");
		while(lwip_comm_init()) //lwip initialization
		{
			LCD_ShowString(30,110,200,20,16,"LWIP Init Falied!");
			printf("[ERROR] LWIP initialization failed! Retrying...\r\n");
			delay_1ms(1200);
			LCD_Fill(30,110,230,130,WHITE); //Clear display
			LCD_ShowString(30,110,200,16,16,"Retrying...");
		}
		printf("[SYSTEM] LWIP network stack initialized successfully\r\n");

		#if LWIP_DHCP   //Use DHCP
			printf("[SYSTEM] Waiting for DHCP to get IP address...\r\n");
			while((lwipdev.dhcpstatus!=2)&&(lwipdev.dhcpstatus!=0XFF))//Wait for DHCP success/timeout
			{
				lwip_periodic_handle();	//LWIP periodic processing function
			}
			if(lwipdev.dhcpstatus == 2) {
				printf("[SYSTEM] DHCP got IP address successfully\r\n");
			} else {
				printf("[SYSTEM] DHCP timeout, using static IP\r\n");
			}
		#endif
		  show_address(lwipdev.dhcpstatus);	//Display address information
		  printf("[SYSTEM] System initialization completed, network ready\r\n");
		  printf("========================================\r\n\r\n");

}

/************************************************************ 
 * ????:       Implement(void)
 * ???:       ???????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void Implement(void)
{
	 static uint32_t status_print_timer = 0;
	 static uint32_t link_check_timer = 0;
	 static u8 last_link_status = 0;
	 extern u32 lwip_localtime;  // 外部声明lwip本地时间变量

	 printf("[SYSTEM] Entering main loop, starting network services...\r\n");
	 printf("[INFO] You can use ping command to test network connectivity\r\n");
	 printf("[INFO] ping %d.%d.%d.%d\r\n\r\n", lwipdev.ip[0], lwipdev.ip[1], lwipdev.ip[2], lwipdev.ip[3]);

	 while(1)
	 {
        lwip_periodic_handle();	//LWIP???????????

        // Check key press, KEY1 to display network statistics
        if(Key_Scan(GPIOE, GPIO_PIN_4) == KEY_ON) {
            printf("[USER] User pressed KEY1, displaying network statistics\r\n");
            lwip_get_network_stats();
        }

        // Check link status every 5 seconds
        if(lwip_localtime - link_check_timer >= 5000) {
            u8 current_link_status;
            link_check_timer = lwip_localtime;
            current_link_status = LAN8720_Get_Link_Status();

            if(current_link_status != last_link_status) {
                if(current_link_status) {
                    u8 speed, duplex;
                    printf("[LINK] Network link connected\r\n");
                    LAN8720_Get_Speed_Duplex(&speed, &duplex);
                } else {
                    printf("[LINK] Network link disconnected\r\n");
                }
                last_link_status = current_link_status;
            }
        }

        // Print status information every 30 seconds
        if(lwip_localtime - status_print_timer >= 30000) {
            status_print_timer = lwip_localtime;
            printf("[STATUS] System running normally, network services active\r\n");
            printf("[STATUS] Current IP: %d.%d.%d.%d\r\n", lwipdev.ip[0], lwipdev.ip[1], lwipdev.ip[2], lwipdev.ip[3]);
            printf("[STATUS] Link status: %s\r\n", LAN8720_Get_Link_Status() ? "Connected" : "Disconnected");
            #if LWIP_DHCP
            if(lwipdev.dhcpstatus == 2) {
                printf("[STATUS] DHCP status: IP address obtained\r\n");
            } else if(lwipdev.dhcpstatus == 0xFF) {
                printf("[STATUS] DHCP status: Timeout, using static IP\r\n");
            } else {
                printf("[STATUS] DHCP status: Getting IP address...\r\n");
            }
            #endif
            printf("\r\n");
        }
	 }
}


/****************************End*****************************/

