/************************************************************
 * ??????????????????????????? 
 * ?????Implement.c
 * ????: ?????
 * ??: ?????????????????
 * ???: YunXiang_TechShare  
 * Q Q: 2228398717
 * ???????????
************************************************************/

/************************* ???? *************************/

#include "Implement.h"

/************************* ???? *************************/

/************************ ???????? ************************/

uint16_t time_cnt=0;

/************************ ???????? ************************/

/************************************************************ 
 * ????:       show_address(u8 mode)
 * ???:       ??LCD??????????? 
 * ????:       1 ???DHCP?????????
  	           ???? ?????????
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void show_address(u8 mode)
{
	u8 buf[30];
	if(mode==1)
	{
		sprintf((char*)buf,"MAC    :%d.%d.%d.%d.%d.%d",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//???MAC???
		LCD_ShowString(30,130,210,16,16,buf); 
		sprintf((char*)buf,"DHCP IP:%d.%d.%d.%d",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//??????IP???
		LCD_ShowString(30,150,210,16,16,buf); 
		sprintf((char*)buf,"DHCP GW:%d.%d.%d.%d",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//?????????
		LCD_ShowString(30,170,210,16,16,buf); 
		sprintf((char*)buf,"DHCP IP:%d.%d.%d.%d",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//?????????????
		LCD_ShowString(30,190,210,16,16,buf); 
	}
	else 
	{
		sprintf((char*)buf,"MAC      :%d.%d.%d.%d.%d.%d",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//???MAC???
		LCD_ShowString(30,130,210,16,16,buf); 
		sprintf((char*)buf,"Static IP:%d.%d.%d.%d",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//??????IP???
		LCD_ShowString(30,150,210,16,16,buf); 
		sprintf((char*)buf,"Static GW:%d.%d.%d.%d",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//?????????
		LCD_ShowString(30,170,210,16,16,buf); 
		sprintf((char*)buf,"Static IP:%d.%d.%d.%d",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//?????????????
		LCD_ShowString(30,190,210,16,16,buf); 
	}	
}

/************************************************************ 
 * ????:       System_Init(void)
 * ???:       ???????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void System_Init(void)
{
    systick_config();     // ???????
	
	  nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);//????NVIC???????2:2????????????2??????????
	
	  USART0_Config();  // ????????
	  printf("\r\n\r\n");
	  printf("========================================\r\n");
	  printf("    GD32F470ZGT6 LWIP???????\r\n");
	  printf("    ?????: ????????????\r\n");
	  printf("    ????��: V1.0\r\n");
	  printf("    ???????: %s %s\r\n", __DATE__, __TIME__);
	  printf("========================================\r\n\r\n");

	  printf("[SYSTEM] ??????????...\r\n");
	  printf("[SYSTEM] SysTick????????????\r\n");
	  printf("[SYSTEM] NVIC?��?????????????\r\n");
	  printf("[SYSTEM] USART0???????????\r\n");

	  LED_Init();  			//LED?????
	  printf("[SYSTEM] LED????????\r\n");

	  KEY_Init();  			//?????????
	  printf("[SYSTEM] ????????????\r\n");

	 // LCD_Init(); 			//LCD?????
	  printf("[SYSTEM] LCD?????????????\r\n");

	  my_mem_init(SRAMIN);		//????????????
	  my_mem_init(SRAMCCM);	//?????CCM????
	  printf("[SYSTEM] ??????????????\r\n");
	
		POINT_COLOR = RED; 		
		LCD_ShowString(30,30,200,16,16,"LYIT GD32F4");
		LCD_ShowString(30,50,200,16,16,"Ethernet lwIP Test");
		LCD_ShowString(30,70,200,16,16,"LYIT@GD32F470");
		LCD_ShowString(30,90,200,16,16,"2024/03/11"); 	
		Timer3_Init(9999,239); //100hz?????
		printf("[SYSTEM] ?????3????????(100Hz)\r\n");
		printf("[SYSTEM] ???????????????\r\n\r\n");

		printf("[SYSTEM] ????????LWIP????��???...\r\n");
		while(lwip_comm_init()) //lwip?????
		{
			LCD_ShowString(30,110,200,20,16,"LWIP Init Falied!");
			printf("[ERROR] LWIP?????????????????...\r\n");
			delay_1ms(1200);
			LCD_Fill(30,110,230,130,WHITE); //??????
			LCD_ShowString(30,110,200,16,16,"Retrying...");
		}
		printf("[SYSTEM] LWIP????��???????????\r\n");

		#if LWIP_DHCP   //???DHCP
			printf("[SYSTEM] ???DHCP???IP???...\r\n");
			while((lwipdev.dhcpstatus!=2)&&(lwipdev.dhcpstatus!=0XFF))//???DHCP??????/??????
			{
				lwip_periodic_handle();	//LWIP??????????????????
			}
			if(lwipdev.dhcpstatus == 2) {
				printf("[SYSTEM] DHCP???IP??????\r\n");
			} else {
				printf("[SYSTEM] DHCP???IP?????????????IP\r\n");
			}
		#endif
		  show_address(lwipdev.dhcpstatus);	//?????????
		  printf("[SYSTEM] ????????????????????\r\n");
		  printf("========================================\r\n\r\n");

}

/************************************************************ 
 * ????:       Implement(void)
 * ???:       ???????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void Implement(void)
{
	 static uint32_t status_print_timer = 0;
	 static uint32_t link_check_timer = 0;
	 static u8 last_link_status = 0;

	 printf("[SYSTEM] ?????????????????????...\r\n");
	 printf("[INFO] ???????ping????????????????\r\n");
	 printf("[INFO] ping %d.%d.%d.%d\r\n\r\n", lwipdev.ip[0], lwipdev.ip[1], lwipdev.ip[2], lwipdev.ip[3]);

	 while(1)
	 {
        lwip_periodic_handle();	//LWIP定期处理函数

        // 检查按键，按下KEY1显示网络统计信息
        if(KEY_Scan(0) == KEY1_PRES) {
            printf("[USER] 用户按下KEY1，显示网络统计信息\r\n");
            lwip_get_network_stats();
        }

        // ÿ5����һ����·״̬
        if(lwip_localtime - link_check_timer >= 5000) {
            link_check_timer = lwip_localtime;
            u8 current_link_status = LAN8720_Get_Link_Status();

            if(current_link_status != last_link_status) {
                if(current_link_status) {
                    u8 speed, duplex;
                    printf("[LINK] ������·������\r\n");
                    LAN8720_Get_Speed_Duplex(&speed, &duplex);
                } else {
                    printf("[LINK] ������·�ѶϿ�\r\n");
                }
                last_link_status = current_link_status;
            }
        }

        // ?30????????????
        if(lwip_localtime - status_print_timer >= 30000) {
            status_print_timer = lwip_localtime;
            printf("[STATUS] ?????????????????????\r\n");
            printf("[STATUS] ???IP: %d.%d.%d.%d\r\n", lwipdev.ip[0], lwipdev.ip[1], lwipdev.ip[2], lwipdev.ip[3]);
            #if LWIP_DHCP
            if(lwipdev.dhcpstatus == 2) {
                printf("[STATUS] DHCP??: ????IP???\r\n");
            } else if(lwipdev.dhcpstatus == 0xFF) {
                printf("[STATUS] DHCP??: ??????????IP\r\n");
            } else {
                printf("[STATUS] DHCP??: ??????IP???...\r\n");
            }
            #endif
            printf("\r\n");
        }
	 }
}


/****************************End*****************************/

