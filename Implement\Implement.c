/************************************************************
 * ��Ȩ���Ͻ���ҵ��;������ѧϰʹ�á� 
 * �ļ���Implement.c
 * ����: ���Ǿ�
 * ƽ̨: ��Ծ����ҹ���߿�����
 * ΢��: YunXiang_TechShare  
 * Q Q: 2228398717
 * ���ںţ����Ǿ�
************************************************************/

/************************* ͷ�ļ� *************************/

#include "Implement.h"

/************************* �궨�� *************************/

/************************ �������� ************************/

uint16_t time_cnt=0;

/************************ �������� ************************/

/************************************************************ 
 * ����:       show_address(u8 mode)
 * ˵��:       ��LCD����ʾ��ַ��Ϣ 
 * ����:       1 ��ʾDHCP��ȡ���ĵ�ַ
  	           ���� ��ʾ��̬��ַ
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void show_address(u8 mode)
{
	u8 buf[30];
	if(mode==1)
	{
		sprintf((char*)buf,"MAC    :%d.%d.%d.%d.%d.%d",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//��ӡMAC��ַ
		LCD_ShowString(30,130,210,16,16,buf); 
		sprintf((char*)buf,"DHCP IP:%d.%d.%d.%d",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//��ӡ��̬IP��ַ
		LCD_ShowString(30,150,210,16,16,buf); 
		sprintf((char*)buf,"DHCP GW:%d.%d.%d.%d",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//��ӡ���ص�ַ
		LCD_ShowString(30,170,210,16,16,buf); 
		sprintf((char*)buf,"DHCP IP:%d.%d.%d.%d",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//��ӡ���������ַ
		LCD_ShowString(30,190,210,16,16,buf); 
	}
	else 
	{
		sprintf((char*)buf,"MAC      :%d.%d.%d.%d.%d.%d",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//��ӡMAC��ַ
		LCD_ShowString(30,130,210,16,16,buf); 
		sprintf((char*)buf,"Static IP:%d.%d.%d.%d",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//��ӡ��̬IP��ַ
		LCD_ShowString(30,150,210,16,16,buf); 
		sprintf((char*)buf,"Static GW:%d.%d.%d.%d",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//��ӡ���ص�ַ
		LCD_ShowString(30,170,210,16,16,buf); 
		sprintf((char*)buf,"Static IP:%d.%d.%d.%d",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//��ӡ���������ַ
		LCD_ShowString(30,190,210,16,16,buf); 
	}	
}

/************************************************************ 
 * ����:       System_Init(void)
 * ˵��:       ϵͳ��ʼ��
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void System_Init(void)
{
    systick_config();     // ʱ������
	
	  nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);//����NVIC�жϷ���2:2λ��ռ���ȼ���2λ��Ӧ���ȼ�
	
	  USART0_Config();  // ���ڳ�ʼ��
	  printf("\r\n\r\n");
	  printf("========================================\r\n");
	  printf("    GD32F470ZGT6 LWIP网络实验\r\n");
	  printf("    硬件平台: 凌云科技开发板\r\n");
	  printf("    固件版本: V1.0\r\n");
	  printf("    编译时间: %s %s\r\n", __DATE__, __TIME__);
	  printf("========================================\r\n\r\n");

	  printf("[SYSTEM] 系统初始化开始...\r\n");
	  printf("[SYSTEM] SysTick定时器配置完成\r\n");
	  printf("[SYSTEM] NVIC中断优先级配置完成\r\n");
	  printf("[SYSTEM] USART0串口配置完成\r\n");

	  LED_Init();  			//LED��ʼ��
	  printf("[SYSTEM] LED初始化完成\r\n");

	  KEY_Init();  			//������ʼ��
	  printf("[SYSTEM] 按键初始化完成\r\n");

	 // LCD_Init(); 			//LCD��ʼ��
	  printf("[SYSTEM] LCD显示屏初始化完成\r\n");

	  my_mem_init(SRAMIN);		//��ʼ���ڲ��ڴ��
	  my_mem_init(SRAMCCM);	//��ʼ��CCM�ڴ��
	  printf("[SYSTEM] 内存管理初始化完成\r\n");
	
		POINT_COLOR = RED; 		
		LCD_ShowString(30,30,200,16,16,"LYIT GD32F4");
		LCD_ShowString(30,50,200,16,16,"Ethernet lwIP Test");
		LCD_ShowString(30,70,200,16,16,"LYIT@GD32F470");
		LCD_ShowString(30,90,200,16,16,"2024/03/11"); 	
		Timer3_Init(9999,239); //100hz��Ƶ��
		printf("[SYSTEM] 定时器3初始化完成(100Hz)\r\n");
		printf("[SYSTEM] 基础硬件初始化完成\r\n\r\n");

		printf("[SYSTEM] 开始初始化LWIP网络协议栈...\r\n");
		while(lwip_comm_init()) //lwip��ʼ��
		{
			LCD_ShowString(30,110,200,20,16,"LWIP Init Falied!");
			printf("[ERROR] LWIP初始化失败！正在重试...\r\n");
			delay_1ms(1200);
			LCD_Fill(30,110,230,130,WHITE); //�����ʾ
			LCD_ShowString(30,110,200,16,16,"Retrying...");
		}
		printf("[SYSTEM] LWIP网络协议栈初始化成功\r\n");

		#if LWIP_DHCP   //ʹ��DHCP
			printf("[SYSTEM] 等待DHCP获取IP地址...\r\n");
			while((lwipdev.dhcpstatus!=2)&&(lwipdev.dhcpstatus!=0XFF))//�ȴ�DHCP��ȡ�ɹ�/��ʱ���
			{
				lwip_periodic_handle();	//LWIP�ں���Ҫ��ʱ�����ĺ���
			}
			if(lwipdev.dhcpstatus == 2) {
				printf("[SYSTEM] DHCP获取IP地址成功\r\n");
			} else {
				printf("[SYSTEM] DHCP获取IP地址超时，使用静态IP\r\n");
			}
		#endif
		  show_address(lwipdev.dhcpstatus);	//��ʾ��ַ��Ϣ
		  printf("[SYSTEM] 系统初始化完成，网络已就绪\r\n");
		  printf("========================================\r\n\r\n");

}

/************************************************************ 
 * ����:       Implement(void)
 * ˵��:       ִ�к���
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void Implement(void)
{
	 static uint32_t status_print_timer = 0;

	 printf("[SYSTEM] 进入主循环，开始网络服务...\r\n");
	 printf("[INFO] 可以使用ping命令测试网络连通性\r\n");
	 printf("[INFO] ping %d.%d.%d.%d\r\n\r\n", lwipdev.ip[0], lwipdev.ip[1], lwipdev.ip[2], lwipdev.ip[3]);

	 while(1)
	 {
        lwip_periodic_handle();	//LWIP�ں���Ҫ��ʱ�����ĺ���

        // 每30秒打印一次状态信息
        if(lwip_localtime - status_print_timer >= 30000) {
            status_print_timer = lwip_localtime;
            printf("[STATUS] 系统运行正常，网络服务活跃\r\n");
            printf("[STATUS] 当前IP: %d.%d.%d.%d\r\n", lwipdev.ip[0], lwipdev.ip[1], lwipdev.ip[2], lwipdev.ip[3]);
            #if LWIP_DHCP
            if(lwipdev.dhcpstatus == 2) {
                printf("[STATUS] DHCP状态: 已获取IP地址\r\n");
            } else if(lwipdev.dhcpstatus == 0xFF) {
                printf("[STATUS] DHCP状态: 超时，使用静态IP\r\n");
            } else {
                printf("[STATUS] DHCP状态: 正在获取IP地址...\r\n");
            }
            #endif
            printf("\r\n");
        }
	 }
}


/****************************End*****************************/

