/************************************************************
 * ��Ȩ���Ͻ���ҵ��;������ѧϰʹ�á� 
 * �ļ���lwip_comm.h
 * ����: ���Ǿ�
 * ƽ̨: ��Ծ����ҹ���߿�����
 * ΢��: YunXiang_TechShare  
 * Q Q: 2228398717
 * ���ںţ����Ǿ�
************************************************************/
#ifndef _LWIP_COMM_H
#define _LWIP_COMM_H 

/************************* ͷ�ļ� *************************/

#include "lan8720.h" 

/************************* �궨�� *************************/

#define LWIP_MAX_DHCP_TRIES		4   //DHCP������������Դ���

/************************ �������� ************************/
//lwip���ƽṹ��
typedef struct  
{
	u8 mac[6];      //MAC��ַ
	u8 remoteip[4];	//Զ������IP��ַ 
	u8 ip[4];       //����IP��ַ
	u8 netmask[4]; 	//��������
	u8 gateway[4]; 	//Ĭ�����ص�IP��ַ
	
	vu8 dhcpstatus;	//dhcp״̬ 
					//0,δ��ȡDHCP��ַ;
					//1,����DHCP��ȡ״̬
					//2,�ɹ���ȡDHCP��ַ
					//0XFF,��ȡʧ��.
}__lwip_dev;
extern __lwip_dev lwipdev;	//lwip���ƽṹ��

/************************ �������� ************************/

void lwip_pkt_handle(void);  // �����յ����ݺ���� 
void lwip_periodic_handle(void);// LWIP��ѯ����
void lwip_comm_default_ip_set(__lwip_dev *lwipx); // lwip Ĭ��IP����
u8 lwip_comm_mem_malloc(void); // lwip��mem��memp���ڴ�����
void lwip_comm_mem_free(void); // lwip��mem��memp�ڴ��ͷ�
u8 lwip_comm_init(void); // LWIP��ʼ��(LWIP������ʱ��ʹ��)
void lwip_dhcp_process_handle(void); // DHCP��������
void lwip_get_network_stats(void); // 获取网络统计信息

#endif


/****************************End*****************************/


