/************************************************************
 * ??????????????????????????? 
 * ?????lan8720.h
 * ????: ?????
 * ??: ?????????????????
 * ???: YunXiang_TechShare  
 * Q Q: 2228398717
 * ???????????
************************************************************/
#ifndef _LAN8720_H
#define _LAN8720_H

/************************* ???? *************************/

#include "HeaderFiles.h"

/************************* ???? *************************/

#define LAN8720_PHY_ADDRESS  	0x00				//LAN8720 PHY address
#define LAN8720_RST(value) gpio_bit_write(GPIOD,GPIO_PIN_3,value) //LAN8720 reset pin

// PHY register addresses (use standard library definitions)
// PHY_REG_BCR and PHY_REG_BSR are already defined in gd32f4xx_enet.h
#define PHY_REG_ID1          0x02    // PHY Identifier 1
#define PHY_REG_ID2          0x03    // PHY Identifier 2

/************************ ???????? ************************/

extern enet_descriptors_struct  *dma_current_txdesc;	//DMA????????????????
extern enet_descriptors_struct  *dma_current_rxdesc; 	//DMA???????????????? 
extern ETH_DMA_Rx_Frame_infos *DMA_RX_FRAME_infos;	  //DMA????????????????

/************************ ???????? ************************/

u8 LAN8720_Init(void);  // lan8720????? 
u8 ETH_MACDMA_Config(void);  // ?????ETH MAC??DMA????
FrameTypeDef ETH_Rx_Packet(void);  // ????????????????
u8 ETH_Tx_Packet(u16 FrameLength); // ????????????????
u32 ETH_GetCurrentTxBuffer(void);  // ??????????????Tx buffer???
u8 ETH_Mem_Malloc(void); // ?ETH??????????????
void ETH_Mem_Free(void); // ???ETH ???????????????

// ??????��???????????
u8 LAN8720_Get_Link_Status(void);  // ?????��??????
void LAN8720_Get_Speed_Duplex(u8 *speed, u8 *duplex); // ????????????


#endif


/****************************End*****************************/


