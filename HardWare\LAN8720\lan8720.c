/************************************************************
 * ??????????????????????????? 
 * ?????lan8720.c
 * ????: ?????
 * ??: ?????????????????
 * ???: YunXiang_TechShare  
 * Q Q: 2228398717
 * ???????????
************************************************************/

/************************* ???? *************************/

#include "lan8720.h"

/************************* ???? *************************/

/************************ ???????? ************************/

/************************ ???????? ************************/

static void ETHERNET_NVICConfiguration(void);
extern void lwip_pkt_handle(void);		//??lwip_comm.c???????

/************************************************************ 
 * ????:       LAN8720_Init(void)
 * ???:       LAN8720?????
 * ????:       ??
 * ???:       ??
 * ?????:     0,???;  ????,???
 * ????        ?????
 * ????:       
							???????????? RMII???
							ETH_MDIO -------------------------> PA2
							ETH_MDC --------------------------> PC1
							ETH_RMII_REF_CLK------------------> PA1
							ETH_RMII_CRS_DV ------------------> PA7
							ETH_RMII_RXD0 --------------------> PC4
							ETH_RMII_RXD1 --------------------> PC5
							ETH_RMII_TX_EN -------------------> PB11
							ETH_RMII_TXD0 --------------------> PB12
							ETH_RMII_TXD1 --------------------> PB13
							ETH_RESET-------------------------> PD3
************************************************************/

u8 LAN8720_Init(void)
{
	  u8 rval=0;

    printf("[LAN8720] Starting LAN8720 network chip initialization...\r\n");

    rcu_periph_clock_enable(RCU_GPIOA);//Enable GPIO clock RMII interface
    rcu_periph_clock_enable(RCU_GPIOC);
    rcu_periph_clock_enable(RCU_GPIOD);
    rcu_periph_clock_enable(RCU_GPIOB);
	  rcu_periph_clock_enable(RCU_GPIOG);

    printf("[LAN8720] GPIO clock enabled\r\n");

    rcu_periph_clock_enable(RCU_SYSCFG);  //???SYSCFG???
    /* choose DIV5 to get 50MHz from 240MHz on CKOUT0 pin (PA8) to clock the PHY */
    rcu_ckout0_config(RCU_CKOUT0SRC_PLLP, RCU_CKOUT0_DIV5);
    syscfg_enet_phy_interface_config(SYSCFG_ENET_PHY_RMII);    
    
	  // ????PA1 PA2 PA7
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_7);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_7);
	
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_1);  //????????????????
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_2);
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_7);
	
	  // ????PC1,PC4 and PC5
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5);
    gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5);

    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_1);  //????????????????
    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_4);
    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_5);


	  // Configure PG11, PG13 and PG14 (original configuration)
		gpio_mode_set(GPIOG, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_11|GPIO_PIN_13|GPIO_PIN_14);
    gpio_output_options_set(GPIOG, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_11|GPIO_PIN_13|GPIO_PIN_14);

    gpio_af_set(GPIOG, GPIO_AF_11, GPIO_PIN_11);
    gpio_af_set(GPIOG, GPIO_AF_11, GPIO_PIN_13);
    gpio_af_set(GPIOG, GPIO_AF_11, GPIO_PIN_14);
	
  	// ????PD3????????
		gpio_af_set(GPIOD, GPIO_AF_0, GPIO_PIN_3);   // ????????????
    gpio_mode_set(GPIOD, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, GPIO_PIN_3);
    gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_3);

    printf("[LAN8720] GPIO???????????????LAN8720...\r\n");
    LAN8720_RST(0); //???????LAN8720
    delay_1ms(50);
    LAN8720_RST(1);  //????????
    printf("[LAN8720] LAN8720???????\r\n");

		ETHERNET_NVICConfiguration();	//?????????????
		printf("[LAN8720] ????????????????\r\n");

		rval=ETH_MACDMA_Config();		//????MAC??DMA
    if(rval == 0) {
        printf("[LAN8720] MAC??DMA????????\r\n");
    } else {
        printf("[LAN8720] MAC??DMA???????\r\n");
    }

    // ?????��??
    if(rval != 0) {
        delay_1ms(1000);  // ???PHY???

        if(LAN8720_Get_Link_Status()) {
            u8 speed, duplex;
            printf("[LAN8720] ??????��??????\r\n");
            LAN8720_Get_Speed_Duplex(&speed, &duplex);
        } else {
            printf("[LAN8720] ????: ??????����?????????????\r\n");
        }
    }

    return !rval;					//ETH??????:0,???;1,???;???????????
}

/************************************************************ 
 * ????:       ETHERNET_NVICConfiguration(void)
 * ???:       ?????????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

static void ETHERNET_NVICConfiguration(void)
{
    nvic_vector_table_set(NVIC_VECTTAB_FLASH, 0x0);  // ?????????????????
    nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);  // ???????
    nvic_irq_enable(ENET_IRQn, 0, 0); // ??????????????
}

/************************************************************ 
 * ????:       ETH_MACDMA_Config(void)
 * ???:       ?????ETH MAC??DMA????
 * ????:       ??
 * ???:       ??
 * ?????:     ERROR,???(0)		SUCCESS,???(1)
 * ????        ?????
 * ????:       ??
************************************************************/

u8 ETH_MACDMA_Config(void)
{
	 u8 rval;

   printf("[ETH] ????????????MAC??DMA...\r\n");
   rcu_periph_clock_enable(RCU_ENET);    // ???????????
   rcu_periph_clock_enable(RCU_ENETTX);
   rcu_periph_clock_enable(RCU_ENETRX);
   printf("[ETH] ??????????????\r\n");

	 enet_deinit();  // AHB????????????
	 enet_software_reset(); // ?????????????
//	 while(enet_software_reset() == SUCCESS);//??????????????
   printf("[ETH] ????????????????\r\n");

	 rval = enet_init(ENET_AUTO_NEGOTIATION, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS);   /*?????MAC???????*/

	if(rval==SUCCESS) // ???????
	 {
     printf("[ETH] ??????????????????????????\r\n");
		 enet_interrupt_enable(ENET_DMA_INT_NIE);
		 enet_interrupt_enable(ENET_DMA_INT_RIE);  	//????????????????
     printf("[ETH] ???????????????\r\n");
	 } else {
     printf("[ETH] ?????????????????????: %d\r\n", rval);
   }
	 return rval;
	
}
/************************************************************ 
 * ????:       ETH_MACDMA_Config(void)
 * ???:       ?????ETH MAC??DMA????
 * ????:       ??
 * ???:       ??
 * ?????:     ERROR,???(0)		SUCCESS,???(1)
 * ????        ?????
 * ????:       ??
************************************************************/

//?????DMA?????????????
void ENET_IRQHandler(void)
{
	static uint32_t rx_packet_count = 0;
	static uint32_t rx_error_count = 0;

  while(enet_rxframe_size_get() != 0) 	//???????????????
	 {
		// ???????��??????
		if(enet_interrupt_flag_get(ENET_DMA_INT_FLAG_RBU) != RESET) {
			rx_error_count++;
			printf("[ETH] ????????????????????????: %ld\r\n", rx_error_count);
		}

		lwip_pkt_handle();
		rx_packet_count++;

		// ÿ�յ�1000������ӡһ��ͳ����Ϣ
		if(rx_packet_count % 1000 == 0) {
			printf("[ETH] ����ͳ�� - ����: %ld ��, ����: %ld ��\r\n",
			       rx_packet_count, rx_error_count);
		}
	 }
    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_RS_CLR);  //???DMA????????
    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_NI_CLR);  //???DMA????????????
}

/************************************************************ 
 * ????:       ETH_Rx_Packet(void)
 * ???:       ????????????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??????????????
 * ????        ?????
 * ????:       ??
************************************************************/

FrameTypeDef ETH_Rx_Packet(void)
{ 
	u32 framelength=0;
	FrameTypeDef frame={0,0};   
	//???n???????,???????ETHERNET DMA(????????)/CPU(?????????)
	if((dma_current_rxdesc->status&ENET_RDES0_DAV)!=(u32)RESET)
	{	
		frame.length=ETH_ERROR; 
		if ((ETH->DMASR&ENET_DMA_STAT_RBU)!=(u32)RESET)  
		{ 
			ETH->DMASR = ENET_DMA_STAT_RBU;//???ETH DMA??RBUS?? 
			ETH->DMARPDR=0;//???DMA????
		}
		return frame;//????,OWN??????????
	}  
	if(((dma_current_rxdesc->status&ENET_RDES0_ERRS)==(u32)RESET)&& 
	((dma_current_rxdesc->status & ENET_RDES0_LDES)!=(u32)RESET)&&  
	((dma_current_rxdesc->status & ENET_RDES0_FDES)!=(u32)RESET))  
	{       
		framelength=((dma_current_rxdesc->status&ENET_RDES0_FRML)>>ETH_DMARxDesc_FrameLengthShift)-4;//?????????????(??????4???CRC)
 		frame.buffer = dma_current_rxdesc->buffer1_addr;//??????????????????
	}else framelength=ETH_ERROR;//????  
	frame.length=framelength; 
	frame.descriptor=dma_current_rxdesc;  
	//????ETH DMA???Rx????????????Rx??????
	//??????buffer????????????DMA Rx??????
	dma_current_rxdesc=(enet_descriptors_struct*)(dma_current_rxdesc->buffer2_next_desc_addr);   
	return frame;  
}

/************************************************************ 
 * ????:       ETH_Tx_Packet(u16 FrameLength)
 * ???:       ????????????????
 * ????:       FrameLength:?????????
 * ???:       ??
 * ?????:     ETH_ERROR,???????(0) ETH_SUCCESS,??????(1)
 * ????        ?????
 * ????:       ??
************************************************************/

u8 ETH_Tx_Packet(u16 FrameLength)
{   
	//???n???????,???????ETHERNET DMA(????????)/CPU(?????????)
	if((dma_current_txdesc->status&ENET_TDES0_DAV)!=(u32)RESET)return ETH_ERROR;//????,OWN?????????? 
 	dma_current_txdesc->control_buffer_size=(FrameLength&ENET_TDES1_TB1S);//?????????,bits[12:0]
	dma_current_txdesc->status|=ENET_TDES0_LSG|ENET_TDES0_FSG;//????????????????????????(1??????????????)
  	dma_current_txdesc->status|=ENET_TDES0_DAV;//????Tx????????OWN??,buffer???ETH DMA
	if((ETH->DMASR&ENET_DMA_STAT_TBU)!=(u32)RESET)//??Tx Buffer????????(TBUS)??????????,??????.???????
	{ 
		ETH->DMASR=ENET_DMA_STAT_TBU;//????ETH DMA TBUS?? 
		ETH->DMATPDR=0;//???DMA????
	} 
	//????ETH DMA???Tx????????????Tx??????
	//??????buffer?????????????DMA Tx?????? 
	dma_current_txdesc=(enet_descriptors_struct*)(dma_current_txdesc->buffer2_next_desc_addr);    
	return ETH_SUCCESS;   
}

/************************************************************ 
 * ????:       ETH_GetCurrentTxBuffer(void)
 * ???:       ??????????????Tx buffer???
 * ????:       ??
 * ???:       ??
 * ?????:     Tx buffer???
 * ????        ?????
 * ????:       ??
************************************************************/

u32 ETH_GetCurrentTxBuffer(void)
{  
  return dma_current_txdesc->buffer1_addr;//????Tx buffer???  
}

/************************************************************ 
 * ????:       ETH_Mem_Malloc(void)
 * ???:       ?ETH??????????????
 * ????:       ??
 * ???:       ??
 * ?????:     0,????    ????,???
 * ????        ?????
 * ????:       ??
************************************************************/

u8 ETH_Mem_Malloc(void)
{ 
	rxdesc_tab=mymalloc(SRAMIN,ENET_RXBUF_NUM*sizeof(enet_descriptors_struct));//???????
	txdesc_tab=mymalloc(SRAMIN,ENET_TXBUF_NUM*sizeof(enet_descriptors_struct));//???????  

	if(!rxdesc_tab||!txdesc_tab)  //||!Rx_Buff||!Tx_Buff 
	{
		ETH_Mem_Free();
		return 1;	//???????
	}	
	return 0;		//??????
}

/************************************************************ 
 * ????:       ETH_Mem_Free(void)
 * ???:       ???ETH ???????????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/
void ETH_Mem_Free(void)
{ 
	myfree(SRAMIN,rxdesc_tab);//??????
	myfree(SRAMIN,txdesc_tab);//??????
 
}

/************************************************************
 * ????:       LAN8720_Get_Link_Status(void)
 * ???:       ?????????????
 * ????:       ??
 * ???:       ??
 * ?????:     1-????????, 0-???????
 * ????        ?????
 * ????:       ??
************************************************************/
u8 LAN8720_Get_Link_Status(void)
{
    u16 regval;
    enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_BSR, &regval);
    if(regval & PHY_LINKED_STATUS) {
        return 1; // ????????
    }
    return 0; // ???????
}

/************************************************************
 * ????:       LAN8720_Get_Speed_Duplex(u8 *speed, u8 *duplex)
 * ???:       ????????????????
 * ????:       speed-??????, duplex-????????
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/
void LAN8720_Get_Speed_Duplex(u8 *speed, u8 *duplex)
{
    u16 regval;
    enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_BSR, &regval);

    if(regval & PHY_AUTONEGO_COMPLETE) {
        // ????????????
        enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, 31, &regval); // ???????????
        *speed = (regval & 0x0004) ? 100 : 10;  // bit2: 1=100M, 0=10M
        *duplex = (regval & 0x0010) ? 1 : 0;    // bit4: 1=????, 0=?????

        printf("[LAN8720] ??????????: %dMbps %s???\r\n",
               *speed, *duplex ? "?" : "??");
    } else {
        *speed = 10;
        *duplex = 0;
        printf("[LAN8720] ???????????????????: 10Mbps ?????\r\n");
    }
}

/****************************End*****************************/

