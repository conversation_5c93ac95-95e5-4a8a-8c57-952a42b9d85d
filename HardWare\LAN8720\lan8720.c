/************************************************************
 * ??????????????????????????? 
 * ?????lan8720.c
 * ????: ?????
 * ??: ?????????????????
 * ???: YunXiang_TechShare  
 * Q Q: 2228398717
 * ???????????
************************************************************/

/************************* ???? *************************/

#include "lan8720.h"

/************************* ???? *************************/

/************************ ???????? ************************/

/************************ ???????? ************************/

static void ETHERNET_NVICConfiguration(void);
extern void lwip_pkt_handle(void);		//??lwip_comm.c???????

/************************************************************ 
 * ????:       LAN8720_Init(void)
 * ???:       LAN8720?????
 * ????:       ??
 * ???:       ??
 * ?????:     0,???;  ????,???
 * ????        ?????
 * ????:       
							Network Interface Connection RMII Interface
							ETH_MDIO -------------------------> PA2
							ETH_MDC --------------------------> PC1
							ETH_RMII_REF_CLK------------------> PA1
							ETH_RMII_CRS_DV ------------------> PA7
							ETH_RMII_RXD0 --------------------> PC4
							ETH_RMII_RXD1 --------------------> PC5
							ETH_RMII_TX_EN -------------------> PB11
							ETH_RMII_TXD0 --------------------> PB12
							ETH_RMII_TXD1 --------------------> PB13
							ETH_RESET-------------------------> PD3
************************************************************/

u8 LAN8720_Init(void)
{
	  u8 rval=0;

    printf("[LAN8720] Starting LAN8720 network chip initialization...\r\n");

    rcu_periph_clock_enable(RCU_GPIOA);//Enable GPIO clock RMII interface
    rcu_periph_clock_enable(RCU_GPIOC);
    rcu_periph_clock_enable(RCU_GPIOD);
    rcu_periph_clock_enable(RCU_GPIOB);
	  rcu_periph_clock_enable(RCU_GPIOG);

    printf("[LAN8720] GPIO clock enabled\r\n");

    rcu_periph_clock_enable(RCU_SYSCFG);  // Enable SYSCFG clock

    // Configure PA8 to output 50MHz clock for PHY (if needed)
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_8);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_8);
    gpio_af_set(GPIOA, GPIO_AF_0, GPIO_PIN_8);  // AF0 for MCO

    /* choose DIV5 to get 50MHz from 240MHz on CKOUT0 pin (PA8) to clock the PHY */
    rcu_ckout0_config(RCU_CKOUT0SRC_PLLP, RCU_CKOUT0_DIV5);
    printf("[LAN8720] 50MHz clock output configured on PA8\r\n");

    // Check system clock
    printf("[LAN8720] System clock: %ld Hz\r\n", SystemCoreClock);

    // Wait for clock to stabilize
    delay_1ms(100);

    syscfg_enet_phy_interface_config(SYSCFG_ENET_PHY_RMII);
    printf("[LAN8720] RMII interface mode configured\r\n");
    
	  // Configure PA1 (RMII_REF_CLK - input), PA2 (MDIO), PA7 (CRS_DV - input)
    // PA1 - RMII_REF_CLK (input from PHY)
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_1);
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_1);

    // PA2 - MDIO (bidirectional, should be open-drain)
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_2);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, GPIO_PIN_2);
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_2);

    // PA7 - CRS_DV (input from PHY)
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_7);
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_7);
	
	  // Configure PC1 (MDC - output), PC4 (RXD0 - input), PC5 (RXD1 - input)
    // PC1 - MDC (output to PHY)
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_1);
    gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_1);
    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_1);

    // PC4 - RXD0 (input from PHY)
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_4);
    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_4);

    // PC5 - RXD1 (input from PHY)
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_5);
    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_5);


	  // Configure PB11, PB12 and PB13 (correct configuration)
		gpio_mode_set(GPIOB, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13);

    gpio_af_set(GPIOB, GPIO_AF_11, GPIO_PIN_11);  // ETH_RMII_TX_EN
    gpio_af_set(GPIOB, GPIO_AF_11, GPIO_PIN_12);  // ETH_RMII_TXD0
    gpio_af_set(GPIOB, GPIO_AF_11, GPIO_PIN_13);  // ETH_RMII_TXD1
	
  	// ????PD3????????
		gpio_af_set(GPIOD, GPIO_AF_0, GPIO_PIN_3);   // ????????????
    gpio_mode_set(GPIOD, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, GPIO_PIN_3);
    gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_3);

    printf("[LAN8720] GPIO configured, starting LAN8720 reset...\r\n");
    LAN8720_RST(RESET); // Hardware reset LAN8720
    delay_1ms(100);     // Longer reset time
    LAN8720_RST(SET);   // Release reset
    delay_1ms(500);     // Wait for PHY to stabilize after reset
    printf("[LAN8720] LAN8720 reset completed, waiting for stabilization...\r\n");

		ETHERNET_NVICConfiguration();	// Configure ethernet interrupt
		printf("[LAN8720] Ethernet interrupt configured\r\n");

		rval=ETH_MACDMA_Config();		// Configure MAC and DMA
    if(rval == 0) {
        printf("[LAN8720] MAC and DMA configuration failed!\r\n");
    } else {
        printf("[LAN8720] MAC and DMA configured successfully\r\n");
    }

    // Check PHY and link status
    if(rval != 0) {
        u16 phy_id1, phy_id2;

        delay_1ms(1000);  // Wait for PHY to stabilize

        // Try to read PHY ID to verify PHY is responding
        if(enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_ID1, &phy_id1) == SUCCESS &&
           enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_ID2, &phy_id2) == SUCCESS) {
            printf("[LAN8720] PHY detected - ID1: 0x%04X, ID2: 0x%04X\r\n", phy_id1, phy_id2);

            if(LAN8720_Get_Link_Status()) {
                u8 speed, duplex;
                printf("[LAN8720] Network link connected\r\n");
                LAN8720_Get_Speed_Duplex(&speed, &duplex);
            } else {
                printf("[LAN8720] WARNING: Network link not connected, please check cable\r\n");
            }
        } else {
            printf("[LAN8720] ERROR: Cannot communicate with PHY chip!\r\n");
            printf("[LAN8720] Check PHY power supply and connections\r\n");
        }
    }

    return !rval;					//ETH??????:0,???;1,???;???????????
}

/************************************************************ 
 * ????:       ETHERNET_NVICConfiguration(void)
 * ???:       ?????????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

static void ETHERNET_NVICConfiguration(void)
{
    nvic_vector_table_set(NVIC_VECTTAB_FLASH, 0x0);  // ?????????????????
    nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);  // ???????
    nvic_irq_enable(ENET_IRQn, 0, 0); // ??????????????
}

/************************************************************ 
 * ????:       ETH_MACDMA_Config(void)
 * ???:       ?????ETH MAC??DMA????
 * ????:       ??
 * ???:       ??
 * ?????:     ERROR,???(0)		SUCCESS,???(1)
 * ????        ?????
 * ????:       ??
************************************************************/

u8 ETH_MACDMA_Config(void)
{
	 u8 rval;
	 u16 phy_reg, phy_id1, phy_id2;
	 u8 addr;
	 u16 phy_bsr, bcr_val;

   printf("[ETH] Starting MAC and DMA configuration...\r\n");
   rcu_periph_clock_enable(RCU_ENET);    // Enable ethernet clock
   rcu_periph_clock_enable(RCU_ENETTX);
   rcu_periph_clock_enable(RCU_ENETRX);
   printf("[ETH] Ethernet clock enabled\r\n");

	 enet_deinit();  // AHB reset ethernet
	 printf("[ETH] Ethernet deinitialized\r\n");

	 // Perform software reset
	 printf("[ETH] Performing ethernet software reset...\r\n");
	 enet_software_reset();

	 // Wait for reset to complete (different approach)
	 delay_1ms(100);  // Give some time for reset to take effect

	 // Check if reset completed by reading a register
	 printf("[ETH] Ethernet software reset completed\r\n");

	 // Test PHY communication before enet_init
	 printf("[ETH] Testing PHY communication...\r\n");

	 // Try to read PHY Basic Control Register
	 if(enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_BCR, &phy_reg) == SUCCESS) {
		 printf("[ETH] PHY BCR read successful: 0x%04X\r\n", phy_reg);

		 // Try to read PHY ID registers
		 if(enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_ID1, &phy_id1) == SUCCESS &&
			enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_ID2, &phy_id2) == SUCCESS) {
			printf("[ETH] PHY ID1: 0x%04X, ID2: 0x%04X\r\n", phy_id1, phy_id2);
		 } else {
			printf("[ETH] Failed to read PHY ID registers\r\n");
		 }
	 } else {
		 printf("[ETH] ERROR: Cannot communicate with PHY at address 0x%02X\r\n", LAN8720_PHY_ADDRESS);
	 }

	 // Always scan for PHY regardless of initial result
	 printf("[ETH] Scanning all PHY addresses...\r\n");
	 for(addr = 0; addr < 32; addr++) {
		 if(enet_phy_write_read(ENET_PHY_READ, addr, PHY_REG_ID1, &phy_reg) == SUCCESS) {
			 if(phy_reg != 0xFFFF && phy_reg != 0x0000) {
				 u16 id2;
				 enet_phy_write_read(ENET_PHY_READ, addr, PHY_REG_ID2, &id2);
				 printf("[ETH] Found PHY at address 0x%02X, ID1: 0x%04X, ID2: 0x%04X\r\n", addr, phy_reg, id2);
			 } else {
				 printf("[ETH] Address 0x%02X: ID1 = 0x%04X (no PHY)\r\n", addr, phy_reg);
			 }
		 } else {
			 printf("[ETH] Address 0x%02X: Read failed\r\n", addr);
		 }
	 }

	 // Check PHY status before enet_init
	 if(enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_BSR, &phy_bsr) == SUCCESS) {
		 printf("[ETH] PHY BSR (Status): 0x%04X\r\n", phy_bsr);
		 printf("[ETH] Link status: %s\r\n", (phy_bsr & 0x0004) ? "UP" : "DOWN");
		 printf("[ETH] Auto-negotiation: %s\r\n", (phy_bsr & 0x0020) ? "Complete" : "In progress");

		 // If no link, wait a bit and check again
		 if(!(phy_bsr & 0x0004)) {
			 printf("[ETH] No link detected, waiting for cable connection...\r\n");
			 delay_1ms(1000);
			 enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_BSR, &phy_bsr);
			 printf("[ETH] PHY BSR after wait: 0x%04X\r\n", phy_bsr);
		 }
	 }

	 // Try to restart auto-negotiation manually
	 printf("[ETH] Manually restarting PHY auto-negotiation...\r\n");
	 bcr_val = 0x3200;  // Auto-negotiation enable + restart
	 enet_phy_write_read(ENET_PHY_WRITE, LAN8720_PHY_ADDRESS, PHY_REG_BCR, &bcr_val);
	 delay_1ms(100);

	 printf("[ETH] Starting ethernet initialization...\r\n");

	 // Try initialization without link dependency first
	 printf("[ETH] Attempting initialization without link requirement...\r\n");
	 rval = enet_init(ENET_100M_FULLDUPLEX, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS);
	 printf("[ETH] 100M Full Duplex enet_init() returned: %d (SUCCESS=1, ERROR=0)\r\n", rval);

	 if(rval == ERROR) {
		 printf("[ETH] Trying 10M Full Duplex...\r\n");
		 rval = enet_init(ENET_10M_FULLDUPLEX, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS);
		 printf("[ETH] 10M Full Duplex enet_init() returned: %d\r\n", rval);

		 if(rval == ERROR) {
			 printf("[ETH] Trying auto-negotiation (may fail without link)...\r\n");
			 rval = enet_init(ENET_AUTO_NEGOTIATION, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS);
			 printf("[ETH] Auto-negotiation enet_init() returned: %d\r\n", rval);
		 }
	 }

	if(rval==SUCCESS) // ???????
	 {
     printf("[ETH] ??????????????????????????\r\n");
		 enet_interrupt_enable(ENET_DMA_INT_NIE);
		 enet_interrupt_enable(ENET_DMA_INT_RIE);  	//????????????????
     printf("[ETH] ???????????????\r\n");
	 } else {
     printf("[ETH] ?????????????????????: %d\r\n", rval);
   }
	 return rval;
	
}
/************************************************************ 
 * ????:       ETH_MACDMA_Config(void)
 * ???:       ?????ETH MAC??DMA????
 * ????:       ??
 * ???:       ??
 * ?????:     ERROR,???(0)		SUCCESS,???(1)
 * ????        ?????
 * ????:       ??
************************************************************/

//?????DMA?????????????
void ENET_IRQHandler(void)
{
	static uint32_t rx_packet_count = 0;
	static uint32_t rx_error_count = 0;

  while(enet_rxframe_size_get() != 0) 	//???????????????
	 {
		// ???????????????
		if(enet_interrupt_flag_get(ENET_DMA_INT_FLAG_RBU) != RESET) {
			rx_error_count++;
			printf("[ETH] ????????????????????????: %ld\r\n", rx_error_count);
		}

		lwip_pkt_handle();
		rx_packet_count++;

		// ????1000????????????????
		if(rx_packet_count % 1000 == 0) {
			printf("[ETH] ??????? - ????: %ld ??, ????: %ld ??\r\n",
			       rx_packet_count, rx_error_count);
		}
	 }
    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_RS_CLR);  //???DMA????????
    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_NI_CLR);  //???DMA????????????
}

/************************************************************ 
 * ????:       ETH_Rx_Packet(void)
 * ???:       ????????????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??????????????
 * ????        ?????
 * ????:       ??
************************************************************/

FrameTypeDef ETH_Rx_Packet(void)
{ 
	u32 framelength=0;
	FrameTypeDef frame={0,0};   
	//???n???????,???????ETHERNET DMA(????????)/CPU(?????????)
	if((dma_current_rxdesc->status&ENET_RDES0_DAV)!=(u32)RESET)
	{	
		frame.length=ETH_ERROR; 
		if ((ETH->DMASR&ENET_DMA_STAT_RBU)!=(u32)RESET)  
		{ 
			ETH->DMASR = ENET_DMA_STAT_RBU;//???ETH DMA??RBUS?? 
			ETH->DMARPDR=0;//???DMA????
		}
		return frame;//????,OWN??????????
	}  
	if(((dma_current_rxdesc->status&ENET_RDES0_ERRS)==(u32)RESET)&& 
	((dma_current_rxdesc->status & ENET_RDES0_LDES)!=(u32)RESET)&&  
	((dma_current_rxdesc->status & ENET_RDES0_FDES)!=(u32)RESET))  
	{       
		framelength=((dma_current_rxdesc->status&ENET_RDES0_FRML)>>ETH_DMARxDesc_FrameLengthShift)-4;//?????????????(??????4???CRC)
 		frame.buffer = dma_current_rxdesc->buffer1_addr;//??????????????????
	}else framelength=ETH_ERROR;//????  
	frame.length=framelength; 
	frame.descriptor=dma_current_rxdesc;  
	//????ETH DMA???Rx????????????Rx??????
	//??????buffer????????????DMA Rx??????
	dma_current_rxdesc=(enet_descriptors_struct*)(dma_current_rxdesc->buffer2_next_desc_addr);   
	return frame;  
}

/************************************************************ 
 * ????:       ETH_Tx_Packet(u16 FrameLength)
 * ???:       ????????????????
 * ????:       FrameLength:?????????
 * ???:       ??
 * ?????:     ETH_ERROR,???????(0) ETH_SUCCESS,??????(1)
 * ????        ?????
 * ????:       ??
************************************************************/

u8 ETH_Tx_Packet(u16 FrameLength)
{   
	//???n???????,???????ETHERNET DMA(????????)/CPU(?????????)
	if((dma_current_txdesc->status&ENET_TDES0_DAV)!=(u32)RESET)return ETH_ERROR;//????,OWN?????????? 
 	dma_current_txdesc->control_buffer_size=(FrameLength&ENET_TDES1_TB1S);//?????????,bits[12:0]
	dma_current_txdesc->status|=ENET_TDES0_LSG|ENET_TDES0_FSG;//????????????????????????(1??????????????)
  	dma_current_txdesc->status|=ENET_TDES0_DAV;//????Tx????????OWN??,buffer???ETH DMA
	if((ETH->DMASR&ENET_DMA_STAT_TBU)!=(u32)RESET)//??Tx Buffer????????(TBUS)??????????,??????.???????
	{ 
		ETH->DMASR=ENET_DMA_STAT_TBU;//????ETH DMA TBUS?? 
		ETH->DMATPDR=0;//???DMA????
	} 
	//????ETH DMA???Tx????????????Tx??????
	//??????buffer?????????????DMA Tx?????? 
	dma_current_txdesc=(enet_descriptors_struct*)(dma_current_txdesc->buffer2_next_desc_addr);    
	return ETH_SUCCESS;   
}

/************************************************************ 
 * ????:       ETH_GetCurrentTxBuffer(void)
 * ???:       ??????????????Tx buffer???
 * ????:       ??
 * ???:       ??
 * ?????:     Tx buffer???
 * ????        ?????
 * ????:       ??
************************************************************/

u32 ETH_GetCurrentTxBuffer(void)
{  
  return dma_current_txdesc->buffer1_addr;//????Tx buffer???  
}

/************************************************************ 
 * ????:       ETH_Mem_Malloc(void)
 * ???:       ?ETH??????????????
 * ????:       ??
 * ???:       ??
 * ?????:     0,????    ????,???
 * ????        ?????
 * ????:       ??
************************************************************/

u8 ETH_Mem_Malloc(void)
{ 
	rxdesc_tab=mymalloc(SRAMIN,ENET_RXBUF_NUM*sizeof(enet_descriptors_struct));//???????
	txdesc_tab=mymalloc(SRAMIN,ENET_TXBUF_NUM*sizeof(enet_descriptors_struct));//???????  

	if(!rxdesc_tab||!txdesc_tab)  //||!Rx_Buff||!Tx_Buff 
	{
		ETH_Mem_Free();
		return 1;	//???????
	}	
	return 0;		//??????
}

/************************************************************ 
 * ????:       ETH_Mem_Free(void)
 * ???:       ???ETH ???????????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/
void ETH_Mem_Free(void)
{ 
	myfree(SRAMIN,rxdesc_tab);//??????
	myfree(SRAMIN,txdesc_tab);//??????
 
}

/************************************************************
 * ????:       LAN8720_Get_Link_Status(void)
 * ???:       ?????????????
 * ????:       ??
 * ???:       ??
 * ?????:     1-????????, 0-???????
 * ????        ?????
 * ????:       ??
************************************************************/
u8 LAN8720_Get_Link_Status(void)
{
    u16 regval;
    enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_BSR, &regval);
    if(regval & PHY_LINKED_STATUS) {
        return 1; // ????????
    }
    return 0; // ???????
}

/************************************************************
 * ????:       LAN8720_Get_Speed_Duplex(u8 *speed, u8 *duplex)
 * ???:       ????????????????
 * ????:       speed-??????, duplex-????????
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/
void LAN8720_Get_Speed_Duplex(u8 *speed, u8 *duplex)
{
    u16 regval;
    enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, PHY_REG_BSR, &regval);

    if(regval & PHY_AUTONEGO_COMPLETE) {
        // ????????????
        enet_phy_write_read(ENET_PHY_READ, LAN8720_PHY_ADDRESS, 31, &regval); // ???????????
        *speed = (regval & 0x0004) ? 100 : 10;  // bit2: 1=100M, 0=10M
        *duplex = (regval & 0x0010) ? 1 : 0;    // bit4: 1=????, 0=?????

        printf("[LAN8720] ??????????: %dMbps %s???\r\n",
               *speed, *duplex ? "?" : "??");
    } else {
        *speed = 10;
        *duplex = 0;
        printf("[LAN8720] ???????????????????: 10Mbps ?????\r\n");
    }
}

/****************************End*****************************/

