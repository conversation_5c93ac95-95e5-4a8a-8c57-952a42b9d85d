/************************************************************
 * ????????????????????????��? 
 * ?????lan8720.c
 * ????: ?????
 * ??: ?????????????????
 * ???: YunXiang_TechShare  
 * Q Q: 2228398717
 * ???????????
************************************************************/

/************************* ???? *************************/

#include "lan8720.h"

/************************* ???? *************************/

/************************ ???????? ************************/

/************************ ???????? ************************/

static void ETHERNET_NVICConfiguration(void);
extern void lwip_pkt_handle(void);		//??lwip_comm.c???��??

/************************************************************ 
 * ????:       LAN8720_Init(void)
 * ???:       LAN8720?????
 * ????:       ??
 * ???:       ??
 * ?????:     0,???;  ????,???
 * ????        ?????
 * ????:       
							???????????? RMII???
							ETH_MDIO -------------------------> PA2
							ETH_MDC --------------------------> PC1
							ETH_RMII_REF_CLK------------------> PA1
							ETH_RMII_CRS_DV ------------------> PA7
							ETH_RMII_RXD0 --------------------> PC4
							ETH_RMII_RXD1 --------------------> PC5
							ETH_RMII_TX_EN -------------------> PB11
							ETH_RMII_TXD0 --------------------> PB12
							ETH_RMII_TXD1 --------------------> PB13
							ETH_RESET-------------------------> PD3
************************************************************/

u8 LAN8720_Init(void)
{
	  u8 rval=0;

    printf("[LAN8720] ��ʼ��ʼ��LAN8720����оƬ...\r\n");

    rcu_periph_clock_enable(RCU_GPIOA);//???GPIO??? RMII???
    rcu_periph_clock_enable(RCU_GPIOC);
    rcu_periph_clock_enable(RCU_GPIOD);
    rcu_periph_clock_enable(RCU_GPIOB);
	  rcu_periph_clock_enable(RCU_GPIOG);

    printf("[LAN8720] GPIOʱ��ʹ�����\r\n");

    rcu_periph_clock_enable(RCU_SYSCFG);  //???SYSCFG???
    /* choose DIV5 to get 50MHz from 240MHz on CKOUT0 pin (PA8) to clock the PHY */
    rcu_ckout0_config(RCU_CKOUT0SRC_PLLP, RCU_CKOUT0_DIV5);
    syscfg_enet_phy_interface_config(SYSCFG_ENET_PHY_RMII);    
    
	  // ????PA1 PA2 PA7
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_7);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_7);
	
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_1);  //????????????????
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_2);
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_7);
	
	  // ????PC1,PC4 and PC5
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5);
    gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5);

    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_1);  //????????????????
    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_4);
    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_5);


	  // ????PB11, PB12 and PB13
		gpio_mode_set(GPIOB, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13);

    gpio_af_set(GPIOB, GPIO_AF_11, GPIO_PIN_11);
    gpio_af_set(GPIOB, GPIO_AF_11, GPIO_PIN_12);
    gpio_af_set(GPIOB, GPIO_AF_11, GPIO_PIN_13);
	
  	// ????PD3????????
		gpio_af_set(GPIOD, GPIO_AF_0, GPIO_PIN_3);   // ??��????????
    gpio_mode_set(GPIOD, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, GPIO_PIN_3);
    gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_3);

    printf("[LAN8720] GPIO������ɣ���ʼ��λLAN8720...\r\n");
    LAN8720_RST(0); //?????��LAN8720
    delay_1ms(50);
    LAN8720_RST(1);  //??��????
    printf("[LAN8720] LAN8720��λ���\r\n");

		ETHERNET_NVICConfiguration();	//?????��??????
		printf("[LAN8720] ��̫���ж��������\r\n");

		rval=ETH_MACDMA_Config();		//????MAC??DMA
    if(rval == 0) {
        printf("[LAN8720] MAC��DMA����ʧ�ܣ�\r\n");
    } else {
        printf("[LAN8720] MAC��DMA���óɹ�\r\n");
    }

    return !rval;					//ETH??????:0,???;1,???;???????????
}

/************************************************************ 
 * ????:       ETHERNET_NVICConfiguration(void)
 * ???:       ??????��?????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

static void ETHERNET_NVICConfiguration(void)
{
    nvic_vector_table_set(NVIC_VECTTAB_FLASH, 0x0);  // ?????��??????????
    nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);  // ?��????
    nvic_irq_enable(ENET_IRQn, 0, 0); // ??????��??????
}

/************************************************************ 
 * ????:       ETH_MACDMA_Config(void)
 * ???:       ?????ETH MAC??DMA????
 * ????:       ??
 * ???:       ??
 * ?????:     ERROR,???(0)		SUCCESS,???(1)
 * ????        ?????
 * ????:       ??
************************************************************/

u8 ETH_MACDMA_Config(void)
{
	 u8 rval;

   printf("[ETH] 开始配置以太网MAC和DMA...\r\n");
   rcu_periph_clock_enable(RCU_ENET);    // 使能以太网时钟
   rcu_periph_clock_enable(RCU_ENETTX);
   rcu_periph_clock_enable(RCU_ENETRX);
   printf("[ETH] 以太网时钟使能完成\r\n");

	 enet_deinit();  // AHB总线复位以太网
	 enet_software_reset(); // 软件复位以太网
//	 while(enet_software_reset() == SUCCESS);//等待软件复位完成
   printf("[ETH] 以太网软件复位完成\r\n");

	 rval = enet_init(ENET_AUTO_NEGOTIATION, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS);   /*不使用MAC地址过滤*/

	if(rval==SUCCESS) // 配置成功
	 {
     printf("[ETH] 以太网初始化成功，启用自动协商\r\n");
		 enet_interrupt_enable(ENET_DMA_INT_NIE);
		 enet_interrupt_enable(ENET_DMA_INT_RIE);  	//使能以太网接收中断
     printf("[ETH] 以太网中断使能完成\r\n");
	 } else {
     printf("[ETH] 以太网初始化失败！错误代码: %d\r\n", rval);
   }
	 return rval;
	
}
/************************************************************ 
 * ????:       ETH_MACDMA_Config(void)
 * ???:       ?????ETH MAC??DMA????
 * ????:       ??
 * ???:       ??
 * ?????:     ERROR,???(0)		SUCCESS,???(1)
 * ????        ?????
 * ????:       ??
************************************************************/

//?????DMA?????��??????
void ENET_IRQHandler(void)
{
	
  while(enet_rxframe_size_get() != 0) 	//??????????????
	 { 
		lwip_pkt_handle();		
	 }
    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_RS_CLR);  //???DMA?��???��
    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_NI_CLR);  //???DMA?????��???��
}

/************************************************************ 
 * ????:       ETH_Rx_Packet(void)
 * ???:       ????????????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??????????????
 * ????        ?????
 * ????:       ??
************************************************************/

FrameTypeDef ETH_Rx_Packet(void)
{ 
	u32 framelength=0;
	FrameTypeDef frame={0,0};   
	//??�n???????,???????ETHERNET DMA(????????)/CPU(??��?????)
	if((dma_current_rxdesc->status&ENET_RDES0_DAV)!=(u32)RESET)
	{	
		frame.length=ETH_ERROR; 
		if ((ETH->DMASR&ENET_DMA_STAT_RBU)!=(u32)RESET)  
		{ 
			ETH->DMASR = ENET_DMA_STAT_RBU;//???ETH DMA??RBUS�� 
			ETH->DMARPDR=0;//???DMA????
		}
		return frame;//????,OWN��????????
	}  
	if(((dma_current_rxdesc->status&ENET_RDES0_ERRS)==(u32)RESET)&& 
	((dma_current_rxdesc->status & ENET_RDES0_LDES)!=(u32)RESET)&&  
	((dma_current_rxdesc->status & ENET_RDES0_FDES)!=(u32)RESET))  
	{       
		framelength=((dma_current_rxdesc->status&ENET_RDES0_FRML)>>ETH_DMARxDesc_FrameLengthShift)-4;//?????????????(??????4???CRC)
 		frame.buffer = dma_current_rxdesc->buffer1_addr;//??????????????��??
	}else framelength=ETH_ERROR;//????  
	frame.length=framelength; 
	frame.descriptor=dma_current_rxdesc;  
	//????ETH DMA???Rx????????????Rx??????
	//??????buffer????????????DMA Rx??????
	dma_current_rxdesc=(enet_descriptors_struct*)(dma_current_rxdesc->buffer2_next_desc_addr);   
	return frame;  
}

/************************************************************ 
 * ????:       ETH_Tx_Packet(u16 FrameLength)
 * ???:       ????????????????
 * ????:       FrameLength:?????????
 * ???:       ??
 * ?????:     ETH_ERROR,???????(0) ETH_SUCCESS,??????(1)
 * ????        ?????
 * ????:       ??
************************************************************/

u8 ETH_Tx_Packet(u16 FrameLength)
{   
	//??�n???????,???????ETHERNET DMA(????????)/CPU(??��?????)
	if((dma_current_txdesc->status&ENET_TDES0_DAV)!=(u32)RESET)return ETH_ERROR;//????,OWN��???????? 
 	dma_current_txdesc->control_buffer_size=(FrameLength&ENET_TDES1_TB1S);//?????????,bits[12:0]
	dma_current_txdesc->status|=ENET_TDES0_LSG|ENET_TDES0_FSG;//????????????????��????��(1??????????????)
  	dma_current_txdesc->status|=ENET_TDES0_DAV;//????Tx????????OWN��,buffer???ETH DMA
	if((ETH->DMASR&ENET_DMA_STAT_TBU)!=(u32)RESET)//??Tx Buffer??????��(TBUS)??????????,??????.???????
	{ 
		ETH->DMASR=ENET_DMA_STAT_TBU;//????ETH DMA TBUS�� 
		ETH->DMATPDR=0;//???DMA????
	} 
	//????ETH DMA???Tx????????????Tx??????
	//??????buffer?????????????DMA Tx?????? 
	dma_current_txdesc=(enet_descriptors_struct*)(dma_current_txdesc->buffer2_next_desc_addr);    
	return ETH_SUCCESS;   
}

/************************************************************ 
 * ????:       ETH_GetCurrentTxBuffer(void)
 * ???:       ??????????????Tx buffer???
 * ????:       ??
 * ???:       ??
 * ?????:     Tx buffer???
 * ????        ?????
 * ????:       ??
************************************************************/

u32 ETH_GetCurrentTxBuffer(void)
{  
  return dma_current_txdesc->buffer1_addr;//????Tx buffer???  
}

/************************************************************ 
 * ????:       ETH_Mem_Malloc(void)
 * ???:       ?ETH??????????????
 * ????:       ??
 * ???:       ??
 * ?????:     0,????    ????,???
 * ????        ?????
 * ????:       ??
************************************************************/

u8 ETH_Mem_Malloc(void)
{ 
	rxdesc_tab=mymalloc(SRAMIN,ENET_RXBUF_NUM*sizeof(enet_descriptors_struct));//???????
	txdesc_tab=mymalloc(SRAMIN,ENET_TXBUF_NUM*sizeof(enet_descriptors_struct));//???????  

	if(!rxdesc_tab||!txdesc_tab)  //||!Rx_Buff||!Tx_Buff 
	{
		ETH_Mem_Free();
		return 1;	//???????
	}	
	return 0;		//??????
}

/************************************************************ 
 * ????:       ETH_Mem_Free(void)
 * ???:       ???ETH ???????????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/
void ETH_Mem_Free(void)
{ 
	myfree(SRAMIN,rxdesc_tab);//??????
	myfree(SRAMIN,txdesc_tab);//??????
 
}

/****************************End*****************************/

