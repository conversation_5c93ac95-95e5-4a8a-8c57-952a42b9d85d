/************************************************************
 * ��Ȩ���Ͻ���ҵ��;������ѧϰʹ�á� 
 * �ļ���lan8720.c
 * ����: ���Ǿ�
 * ƽ̨: ��Ծ����ҹ���߿�����
 * ΢��: YunXiang_TechShare  
 * Q Q: 2228398717
 * ���ںţ����Ǿ�
************************************************************/

/************************* ͷ�ļ� *************************/

#include "lan8720.h"

/************************* �궨�� *************************/

/************************ �������� ************************/

/************************ �������� ************************/

static void ETHERNET_NVICConfiguration(void);
extern void lwip_pkt_handle(void);		//��lwip_comm.c���涨��

/************************************************************ 
 * ����:       LAN8720_Init(void)
 * ˵��:       LAN8720��ʼ��
 * ����:       ��
 * ���:       ��
 * ����ֵ:     0,�ɹ�;  ����,ʧ��
 * ����        ���Ǿ�
 * ����:       
							������������ RMII�ӿ�
							ETH_MDIO -------------------------> PA2
							ETH_MDC --------------------------> PC1
							ETH_RMII_REF_CLK------------------> PA1
							ETH_RMII_CRS_DV ------------------> PA7
							ETH_RMII_RXD0 --------------------> PC4
							ETH_RMII_RXD1 --------------------> PC5
							ETH_RMII_TX_EN -------------------> PB11
							ETH_RMII_TXD0 --------------------> PB12
							ETH_RMII_TXD1 --------------------> PB13
							ETH_RESET-------------------------> PD3
************************************************************/

u8 LAN8720_Init(void)
{
	  u8 rval=0;

    rcu_periph_clock_enable(RCU_GPIOA);//ʹ��GPIOʱ�� RMII�ӿ�
    rcu_periph_clock_enable(RCU_GPIOC);
    rcu_periph_clock_enable(RCU_GPIOD);
    rcu_periph_clock_enable(RCU_GPIOB);
	  rcu_periph_clock_enable(RCU_GPIOG);

    rcu_periph_clock_enable(RCU_SYSCFG);  //ʹ��SYSCFGʱ��
    /* choose DIV5 to get 50MHz from 240MHz on CKOUT0 pin (PA8) to clock the PHY */
    rcu_ckout0_config(RCU_CKOUT0SRC_PLLP, RCU_CKOUT0_DIV5);
    syscfg_enet_phy_interface_config(SYSCFG_ENET_PHY_RMII);    
    
	  // ����PA1 PA2 PA7
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_7);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_7);
	
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_1);  //���Ÿ��õ�����ӿ���
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_2);
    gpio_af_set(GPIOA, GPIO_AF_11, GPIO_PIN_7);
	
	  // ����PC1,PC4 and PC5
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5);
    gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5);

    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_1);  //���Ÿ��õ�����ӿ���
    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_4);
    gpio_af_set(GPIOC, GPIO_AF_11, GPIO_PIN_5);


	  // ����PB11, PB12 and PB13
		gpio_mode_set(GPIOB, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13);

    gpio_af_set(GPIOB, GPIO_AF_11, GPIO_PIN_11);
    gpio_af_set(GPIOB, GPIO_AF_11, GPIO_PIN_12);
    gpio_af_set(GPIOB, GPIO_AF_11, GPIO_PIN_13);
	
  	// ����PD3Ϊ�������
		gpio_af_set(GPIOD, GPIO_AF_0, GPIO_PIN_3);   // ��λ���ų�ʼ��
    gpio_mode_set(GPIOD, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, GPIO_PIN_3);
    gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_3);

    LAN8720_RST(0); //Ӳ����λLAN8720
    delay_1ms(50);
    LAN8720_RST(1);  //��λ���� 
		ETHERNET_NVICConfiguration();	//�����ж����ȼ�
		
		rval=ETH_MACDMA_Config();		//����MAC��DMA
       
    return !rval;					//ETH�Ĺ���Ϊ:0,ʧ��;1,�ɹ�;����Ҫȡ��һ�� 
}

/************************************************************ 
 * ����:       ETHERNET_NVICConfiguration(void)
 * ˵��:       ��̫���ж�����
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

static void ETHERNET_NVICConfiguration(void)
{
    nvic_vector_table_set(NVIC_VECTTAB_FLASH, 0x0);  // �����ж���������ַ
    nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);  // �жϷ���
    nvic_irq_enable(ENET_IRQn, 0, 0); // ��̫���ж����ȼ�
}

/************************************************************ 
 * ����:       ETH_MACDMA_Config(void)
 * ˵��:       ��ʼ��ETH MAC�㼰DMA����
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ERROR,ʧ��(0)		SUCCESS,�ɹ�(1)
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

u8 ETH_MACDMA_Config(void)
{
	 u8 rval;

   rcu_periph_clock_enable(RCU_ENET);    // ʹ����̫��ʱ��
   rcu_periph_clock_enable(RCU_ENETTX);
   rcu_periph_clock_enable(RCU_ENETRX);

	 enet_deinit();  // AHB����������̫��
	 enet_software_reset(); // ������������
//	 while(enet_software_reset() == SUCCESS);//�ȴ���������������� 
	 rval = enet_init(ENET_AUTO_NEGOTIATION, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS);   /*��ʹ��MAC��ַ����*/
	
	if(rval==SUCCESS) // ���óɹ�
	 {
		 enet_interrupt_enable(ENET_DMA_INT_NIE);
		 enet_interrupt_enable(ENET_DMA_INT_RIE);  	//ʹ����̫�������ж�	
	 }
	 return rval;
	
}
/************************************************************ 
 * ����:       ETH_MACDMA_Config(void)
 * ˵��:       ��ʼ��ETH MAC�㼰DMA����
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ERROR,ʧ��(0)		SUCCESS,�ɹ�(1)
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

//��̫��DMA�����жϷ�����
void ENET_IRQHandler(void)
{
	
  while(enet_rxframe_size_get() != 0) 	//����Ƿ��յ����ݰ�
	 { 
		lwip_pkt_handle();		
	 }
    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_RS_CLR);  //���DMA�жϱ�־λ
    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_NI_CLR);  //���DMA�����жϱ�־λ
}

/************************************************************ 
 * ����:       ETH_Rx_Packet(void)
 * ˵��:       ����һ���������ݰ�
 * ����:       ��
 * ���:       ��
 * ����ֵ:     �������ݰ�֡�ṹ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

FrameTypeDef ETH_Rx_Packet(void)
{ 
	u32 framelength=0;
	FrameTypeDef frame={0,0};   
	//��鵱ǰ������,�Ƿ�����ETHERNET DMA(���õ�ʱ��)/CPU(��λ��ʱ��)
	if((dma_current_rxdesc->status&ENET_RDES0_DAV)!=(u32)RESET)
	{	
		frame.length=ETH_ERROR; 
		if ((ETH->DMASR&ENET_DMA_STAT_RBU)!=(u32)RESET)  
		{ 
			ETH->DMASR = ENET_DMA_STAT_RBU;//���ETH DMA��RBUSλ 
			ETH->DMARPDR=0;//�ָ�DMA����
		}
		return frame;//����,OWNλ��������
	}  
	if(((dma_current_rxdesc->status&ENET_RDES0_ERRS)==(u32)RESET)&& 
	((dma_current_rxdesc->status & ENET_RDES0_LDES)!=(u32)RESET)&&  
	((dma_current_rxdesc->status & ENET_RDES0_FDES)!=(u32)RESET))  
	{       
		framelength=((dma_current_rxdesc->status&ENET_RDES0_FRML)>>ETH_DMARxDesc_FrameLengthShift)-4;//�õ����հ�֡����(������4�ֽ�CRC)
 		frame.buffer = dma_current_rxdesc->buffer1_addr;//�õ����������ڵ�λ��
	}else framelength=ETH_ERROR;//����  
	frame.length=framelength; 
	frame.descriptor=dma_current_rxdesc;  
	//����ETH DMAȫ��Rx������Ϊ��һ��Rx������
	//Ϊ��һ��buffer��ȡ������һ��DMA Rx������
	dma_current_rxdesc=(enet_descriptors_struct*)(dma_current_rxdesc->buffer2_next_desc_addr);   
	return frame;  
}

/************************************************************ 
 * ����:       ETH_Tx_Packet(u16 FrameLength)
 * ˵��:       ����һ���������ݰ�
 * ����:       FrameLength:���ݰ�����
 * ���:       ��
 * ����ֵ:     ETH_ERROR,����ʧ��(0) ETH_SUCCESS,���ͳɹ�(1)
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

u8 ETH_Tx_Packet(u16 FrameLength)
{   
	//��鵱ǰ������,�Ƿ�����ETHERNET DMA(���õ�ʱ��)/CPU(��λ��ʱ��)
	if((dma_current_txdesc->status&ENET_TDES0_DAV)!=(u32)RESET)return ETH_ERROR;//����,OWNλ�������� 
 	dma_current_txdesc->control_buffer_size=(FrameLength&ENET_TDES1_TB1S);//����֡����,bits[12:0]
	dma_current_txdesc->status|=ENET_TDES0_LSG|ENET_TDES0_FSG;//�������һ���͵�һ��λ����λ(1������������һ֡)
  	dma_current_txdesc->status|=ENET_TDES0_DAV;//����Tx��������OWNλ,buffer�ع�ETH DMA
	if((ETH->DMASR&ENET_DMA_STAT_TBU)!=(u32)RESET)//��Tx Buffer������λ(TBUS)�����õ�ʱ��,������.�ָ�����
	{ 
		ETH->DMASR=ENET_DMA_STAT_TBU;//����ETH DMA TBUSλ 
		ETH->DMATPDR=0;//�ָ�DMA����
	} 
	//����ETH DMAȫ��Tx������Ϊ��һ��Tx������
	//Ϊ��һ��buffer����������һ��DMA Tx������ 
	dma_current_txdesc=(enet_descriptors_struct*)(dma_current_txdesc->buffer2_next_desc_addr);    
	return ETH_SUCCESS;   
}

/************************************************************ 
 * ����:       ETH_GetCurrentTxBuffer(void)
 * ˵��:       �õ���ǰ��������Tx buffer��ַ
 * ����:       ��
 * ���:       ��
 * ����ֵ:     Tx buffer��ַ
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

u32 ETH_GetCurrentTxBuffer(void)
{  
  return dma_current_txdesc->buffer1_addr;//����Tx buffer��ַ  
}

/************************************************************ 
 * ����:       ETH_Mem_Malloc(void)
 * ˵��:       ΪETH�ײ����������ڴ�
 * ����:       ��
 * ���:       ��
 * ����ֵ:     0,����    ����,ʧ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

u8 ETH_Mem_Malloc(void)
{ 
	rxdesc_tab=mymalloc(SRAMIN,ENET_RXBUF_NUM*sizeof(enet_descriptors_struct));//�����ڴ�
	txdesc_tab=mymalloc(SRAMIN,ENET_TXBUF_NUM*sizeof(enet_descriptors_struct));//�����ڴ�  

	if(!rxdesc_tab||!txdesc_tab)  //||!Rx_Buff||!Tx_Buff 
	{
		ETH_Mem_Free();
		return 1;	//����ʧ��
	}	
	return 0;		//����ɹ�
}

/************************************************************ 
 * ����:       ETH_Mem_Free(void)
 * ˵��:       �ͷ�ETH �ײ�����������ڴ�
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/
void ETH_Mem_Free(void)
{ 
	myfree(SRAMIN,rxdesc_tab);//�ͷ��ڴ�
	myfree(SRAMIN,txdesc_tab);//�ͷ��ڴ�
 
}

/****************************End*****************************/

