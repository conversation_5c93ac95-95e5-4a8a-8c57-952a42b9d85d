.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\netif\ethernetif.c
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\netif/ethernetif.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\lwip/err.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\ethernetif.o: ..\LWIP\lwip_app\lwip_comm\lwipopts.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\lwip/debug.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\lwip/arch.h
.\objects\ethernetif.o: ..\LWIP\arch/cc.h
.\objects\ethernetif.o: ..\LWIP\arch/cpu.h
.\objects\ethernetif.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\lwip/netif.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\lwip/def.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h
.\objects\ethernetif.o: ..\HardWare\LAN8720\lan8720.h
.\objects\ethernetif.o: ..\HeaderFiles\HeaderFiles.h
.\objects\ethernetif.o: ..\CMSIS\gd32f4xx.h
.\objects\ethernetif.o: ..\CMSIS\core_cm4.h
.\objects\ethernetif.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\ethernetif.o: ..\CMSIS\core_cmInstr.h
.\objects\ethernetif.o: ..\CMSIS\core_cmFunc.h
.\objects\ethernetif.o: ..\CMSIS\core_cm4_simd.h
.\objects\ethernetif.o: ..\CMSIS\system_gd32f4xx.h
.\objects\ethernetif.o: ..\User\gd32f4xx_libopt.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\ethernetif.o: ..\CMSIS\gd32f4xx.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\ethernetif.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\ethernetif.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\ethernetif.o: ..\User\systick.h
.\objects\ethernetif.o: ..\Implement\Implement.h
.\objects\ethernetif.o: ..\HeaderFiles\HeaderFiles.h
.\objects\ethernetif.o: ..\LWIP\lwip_app\lwip_comm\lwip_comm.h
.\objects\ethernetif.o: ..\HardWare\LAN8720\lan8720.h
.\objects\ethernetif.o: ..\HardWare\LED\LED.h
.\objects\ethernetif.o: ..\HardWare\KEY\KEY.h
.\objects\ethernetif.o: ..\System\TIMER\TIMER.h
.\objects\ethernetif.o: ..\Protocol\USART0\USART0.h
.\objects\ethernetif.o: ..\User\bsp.h
.\objects\ethernetif.o: ..\HardWare\SRAM\SRAM.h
.\objects\ethernetif.o: ..\MALLOC\malloc.h
.\objects\ethernetif.o: ..\HardWare\LCD\LCD.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\netif/etharp.h
.\objects\ethernetif.o: ..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h
.\objects\ethernetif.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
