/************************************************************
 * ��Ȩ���Ͻ���ҵ��;������ѧϰʹ�á� 
 * �ļ���lwip_comm.c
 * ����: ���Ǿ�
 * ƽ̨: ��Ծ����ҹ���߿�����
 * ΢��: YunXiang_TechShare  
 * Q Q: 2228398717
 * ���ںţ����Ǿ�
************************************************************/
#include "lwip_comm.h" 
#include "netif/etharp.h"
#include "lwip/dhcp.h"
#include "lwip/mem.h"
#include "lwip/memp.h"
#include "lwip/init.h"
#include "ethernetif.h" 
#include "lwip/timers.h"
#include "lwip/tcp_impl.h"
#include "lwip/ip_frag.h"
#include "lwip/tcpip.h" 
#include "malloc.h" 
#include <stdio.h>
  
/************************* �궨�� *************************/

/************************ �������� ************************/

__lwip_dev lwipdev;						//lwip���ƽṹ�� 
struct netif lwip_netif;				//����һ��ȫ�ֵ�����ӿ�

extern u32 memp_get_memorysize(void);	//��memp.c���涨��
extern u8_t *memp_memory;				//��memp.c���涨��.
extern u8_t *ram_heap;					//��mem.c���涨��.

u32 TCPTimer=0;			//TCP��ѯ��ʱ��
u32 ARPTimer=0;			//ARP��ѯ��ʱ��
u32 lwip_localtime;		//lwip����ʱ�������,��λ:ms

#if LWIP_DHCP
u32 DHCPfineTimer=0;	//DHCP��ϸ������ʱ��
u32 DHCPcoarseTimer=0;	//DHCP�ֲڴ�����ʱ��
#endif

/************************ �������� ************************/  

/************************************************************ 
 * ����:       lwip_comm_mem_malloc(void)
 * ˵��:       lwip��mem��memp���ڴ�����
 * ����:       ��
 * ���:       ��
 * ����ֵ:     0,�ɹ�   ����,ʧ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

u8 lwip_comm_mem_malloc(void)
{
	u32 mempsize;
	u32 ramheapsize; 
	mempsize=memp_get_memorysize();			//�õ�memp_memory�����С
	memp_memory=mymalloc(SRAMIN,mempsize);	//Ϊmemp_memory�����ڴ�
	ramheapsize=LWIP_MEM_ALIGN_SIZE(MEM_SIZE)+2*LWIP_MEM_ALIGN_SIZE(4*3)+MEM_ALIGNMENT;//�õ�ram heap��С
	ram_heap=mymalloc(SRAMIN,ramheapsize);	//Ϊram_heap�����ڴ� 
	if(!memp_memory||!ram_heap)//������ʧ�ܵ�
	{
		lwip_comm_mem_free();
		return 1;
	}
	return 0;	
}

/************************************************************ 
 * ����:       lwip_comm_mem_free(void)
 * ˵��:       lwip��mem��memp�ڴ��ͷ�
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void lwip_comm_mem_free(void)
{ 	
	myfree(SRAMIN,memp_memory);
	myfree(SRAMIN,ram_heap);
}

/************************************************************ 
 * ����:       lwip_comm_default_ip_set(__lwip_dev *lwipx)
 * ˵��:       lwip Ĭ��IP����
 * ����:       lwipx:lwip���ƽṹ��ָ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void lwip_comm_default_ip_set(__lwip_dev *lwipx)
{
	u32 sn0;
	sn0=*(vu32*)(0x1FFFF7F0);//��ȡSTM32��ΨһID��ǰ24λ��ΪMAC��ַ�����ֽ�
	//Ĭ��Զ��IPΪ:***********00
	lwipx->remoteip[0]=192;	
	lwipx->remoteip[1]=168;
	lwipx->remoteip[2]=1;
	lwipx->remoteip[3]=104;
	//MAC��ַ����(�����ֽڹ̶�Ϊ:2.0.0,�����ֽ���STM32ΨһID)
	lwipx->mac[0]=2;//�����ֽ�(IEEE��֮Ϊ��֯ΨһID,OUI)��ַ�̶�Ϊ:2.0.0
	lwipx->mac[1]=0;
	lwipx->mac[2]=0;
	lwipx->mac[3]=(sn0>>16)&0XFF;//�����ֽ���STM32��ΨһID
	lwipx->mac[4]=(sn0>>8)&0XFFF;;
	lwipx->mac[5]=sn0&0XFF; 
	//Ĭ�ϱ���IPΪ:************
	lwipx->ip[0]=192;	
	lwipx->ip[1]=168;
	lwipx->ip[2]=1;
	lwipx->ip[3]=30;
	//Ĭ����������:*************
	lwipx->netmask[0]=255;	
	lwipx->netmask[1]=255;
	lwipx->netmask[2]=255;
	lwipx->netmask[3]=0;
	//Ĭ������:***********
	lwipx->gateway[0]=192;	
	lwipx->gateway[1]=168;
	lwipx->gateway[2]=1;
	lwipx->gateway[3]=1;	
	lwipx->dhcpstatus=0;//û��DHCP	
} 

/************************************************************ 
 * ����:       lwip_comm_init(void)
 * ˵��:       LWIP��ʼ��(LWIP������ʱ��ʹ��)
 * ����:       ��
 * ���:       ��
 * ����ֵ:    
							0,�ɹ�
							1,�ڴ����
							2,LAN8720��ʼ��ʧ��
							3,��������ʧ��.
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

u8 lwip_comm_init(void)
{
	struct netif *Netif_Init_Flag;		//����netif_add()����ʱ�ķ���ֵ,�����ж������ʼ���Ƿ�ɹ�
	struct ip_addr ipaddr;  			//ip��ַ
	struct ip_addr netmask; 			//��������
	struct ip_addr gw;      			//Ĭ������

	printf("\r\n=== LWIP网络协议栈初始化开始 ===\r\n");

	printf("[LWIP] 分配ETH内存...\r\n");
	if(ETH_Mem_Malloc()) {
		printf("[LWIP] ETH内存分配失败！\r\n");
		return 1;		//�ڴ�����ʧ��
	}
	printf("[LWIP] ETH内存分配成功\r\n");

	printf("[LWIP] 分配LWIP内存...\r\n");
	if(lwip_comm_mem_malloc()) {
		printf("[LWIP] LWIP内存分配失败！\r\n");
		return 1;	//�ڴ�����ʧ��
	}
	printf("[LWIP] LWIP内存分配成功\r\n");

	printf("[LWIP] 初始化LAN8720网络芯片...\r\n");
	if(LAN8720_Init()) {
		printf("[LWIP] LAN8720初始化失败！\r\n");
		return 2;			//��ʼ��LAN8720ʧ��
	}
	printf("[LWIP] LAN8720初始化成功\r\n");

	printf("[LWIP] 初始化LWIP协议栈核心...\r\n");
	lwip_init();						//��ʼ��LWIP�ں�
	printf("[LWIP] LWIP协议栈核心初始化完成\r\n");

	lwip_comm_default_ip_set(&lwipdev);	//����Ĭ��IP����Ϣ
	printf("[LWIP] 默认IP配置完成\r\n");

#if LWIP_DHCP		//ʹ�ö�̬IP
	printf("[LWIP] 配置为DHCP动态获取IP模式\r\n");
	ipaddr.addr = 0;
	netmask.addr = 0;
	gw.addr = 0;
#else				//ʹ�þ�̬IP
	printf("[LWIP] 配置为静态IP模式\r\n");
	IP4_ADDR(&ipaddr,lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);
	IP4_ADDR(&netmask,lwipdev.netmask[0],lwipdev.netmask[1] ,lwipdev.netmask[2],lwipdev.netmask[3]);
	IP4_ADDR(&gw,lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);
	printf("网卡MAC地址为:................%d.%d.%d.%d.%d.%d\r\n",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);
	printf("静态IP地址........................%d.%d.%d.%d\r\n",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);
	printf("子网掩码..........................%d.%d.%d.%d\r\n",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);
	printf("默认网关..........................%d.%d.%d.%d\r\n",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);
#endif
	printf("[LWIP] 添加网络接口到协议栈...\r\n");
	Netif_Init_Flag=netif_add(&lwip_netif,&ipaddr,&netmask,&gw,NULL,&ethernetif_init,&ethernet_input);//�������б�������һ������

#if LWIP_DHCP			//���ʹ��DHCP�Ļ�
	printf("[LWIP] 启动DHCP客户端...\r\n");
	lwipdev.dhcpstatus=0;	//DHCP���Ϊ0
	dhcp_start(&lwip_netif);	//����DHCP����
	printf("[LWIP] DHCP客户端已启动，等待获取IP地址...\r\n");
#endif

	if(Netif_Init_Flag==NULL) {
		printf("[LWIP] 网络接口添加失败！\r\n");
		return 3;//��������ʧ��
	} else {//�������ӳɹ���,����netifΪĬ��ֵ,���Ҵ�netif����
		printf("[LWIP] 网络接口添加成功\r\n");
		netif_set_default(&lwip_netif); //����netifΪĬ������
		netif_set_up(&lwip_netif);		//��netif����
		printf("[LWIP] 网络接口已启用\r\n");
	}
	printf("=== LWIP网络协议栈初始化完成 ===\r\n\r\n");
	return 0;//����OK.
}   

/************************************************************ 
 * ����:       lwip_pkt_handle(void)
 * ˵��:       �����յ����ݺ���� 
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void lwip_pkt_handle(void)
{
  //�����绺�����ж�ȡ���յ������ݰ������䷢�͸�LWIP���� 
 ethernetif_input(&lwip_netif);
}
/************************************************************ 
 * ����:       lwip_periodic_handle()
 * ˵��:       LWIP��ѯ����
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void lwip_periodic_handle()
{
#if LWIP_TCP
	//ÿ250ms����һ��tcp_tmr()����
  if (lwip_localtime - TCPTimer >= TCP_TMR_INTERVAL)
  {
    TCPTimer =  lwip_localtime;
    tcp_tmr();
  }
#endif
  //ARPÿ5s�����Ե���һ��
  if ((lwip_localtime - ARPTimer) >= ARP_TMR_INTERVAL)
  {
    ARPTimer =  lwip_localtime;
    etharp_tmr();
  }

#if LWIP_DHCP //���ʹ��DHCP�Ļ�
  //ÿ500ms����һ��dhcp_fine_tmr()
  if (lwip_localtime - DHCPfineTimer >= DHCP_FINE_TIMER_MSECS)
  {
    DHCPfineTimer =  lwip_localtime;
    dhcp_fine_tmr();
    if ((lwipdev.dhcpstatus != 2)&&(lwipdev.dhcpstatus != 0XFF))
    { 
      lwip_dhcp_process_handle();  //DHCP����
    }
  }

  //ÿ60sִ��һ��DHCP�ֲڴ���
  if (lwip_localtime - DHCPcoarseTimer >= DHCP_COARSE_TIMER_MSECS)
  {
    DHCPcoarseTimer =  lwip_localtime;
    dhcp_coarse_tmr();
  }  
#endif
}

/************************************************************ 
 * ����:       lwip_dhcp_process_handle(void)
 * ˵��:       DHCP��������
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/
//���ʹ����DHCP
#if LWIP_DHCP

void lwip_dhcp_process_handle(void)
{
	u32 ip=0,netmask=0,gw=0;
	switch(lwipdev.dhcpstatus)
	{
		case 0: 	//����DHCP
			printf("[DHCP] 正在查找DHCP服务器...\r\n");
			dhcp_start(&lwip_netif);
			lwipdev.dhcpstatus = 1;		//�ȴ�ͨ��DHCP��ȡ���ĵ�ַ
			printf("[DHCP] DHCP发现过程已启动，请等待...\r\n");
			break;
		case 1:		//�ȴ���ȡ��IP��ַ
		{
			ip=lwip_netif.ip_addr.addr;		//��ȡ��IP��ַ
			netmask=lwip_netif.netmask.addr;//��ȡ��������
			gw=lwip_netif.gw.addr;			//��ȡĬ������ 
			
			if(ip!=0)			//��ȷ��ȡ��IP��ַ��ʱ��
			{
				lwipdev.dhcpstatus=2;	//DHCP�ɹ�
				printf("\r\n=== DHCP获取IP地址成功 ===\r\n");
				printf("[DHCP] 网卡MAC地址为:................%d.%d.%d.%d.%d.%d\r\n",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);
				//������ͨ��DHCP��ȡ����IP��ַ
				lwipdev.ip[3]=(uint8_t)(ip>>24);
				lwipdev.ip[2]=(uint8_t)(ip>>16);
				lwipdev.ip[1]=(uint8_t)(ip>>8);
				lwipdev.ip[0]=(uint8_t)(ip);
				printf("[DHCP] 通过DHCP获取的IP地址..............%d.%d.%d.%d\r\n",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);
				//����ͨ��DHCP��ȡ�������������ַ
				lwipdev.netmask[3]=(uint8_t)(netmask>>24);
				lwipdev.netmask[2]=(uint8_t)(netmask>>16);
				lwipdev.netmask[1]=(uint8_t)(netmask>>8);
				lwipdev.netmask[0]=(uint8_t)(netmask);
				printf("[DHCP] 通过DHCP获取的子网掩码............%d.%d.%d.%d\r\n",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);
				//������ͨ��DHCP��ȡ����Ĭ������
				lwipdev.gateway[3]=(uint8_t)(gw>>24);
				lwipdev.gateway[2]=(uint8_t)(gw>>16);
				lwipdev.gateway[1]=(uint8_t)(gw>>8);
				lwipdev.gateway[0]=(uint8_t)(gw);
				printf("[DHCP] 通过DHCP获取的默认网关..........%d.%d.%d.%d\r\n",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);
				printf("=== 网络配置完成，可以进行网络通信 ===\r\n\r\n");
			}else if(lwip_netif.dhcp->tries>LWIP_MAX_DHCP_TRIES) //ͨ��DHCP�����ȡIP��ַʧ��,�ҳ�������Դ���
			{
				lwipdev.dhcpstatus=0XFF;//DHCP��ʱʧ��.
				//ʹ�þ�̬IP��ַ
				IP4_ADDR(&(lwip_netif.ip_addr),lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);
				IP4_ADDR(&(lwip_netif.netmask),lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);
				IP4_ADDR(&(lwip_netif.gw),lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);
				printf("DHCP����ʱ,ʹ�þ�̬IP��ַ!\r\n");
				printf("����en��MAC��ַΪ:................%d.%d.%d.%d.%d.%d\r\n",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);
				printf("��̬IP��ַ........................%d.%d.%d.%d\r\n",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);
				printf("��������..........................%d.%d.%d.%d\r\n",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);
				printf("Ĭ������..........................%d.%d.%d.%d\r\n",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);
			}
		}
		break;
		default : break;
	}
}
#endif 



/****************************End*****************************/



