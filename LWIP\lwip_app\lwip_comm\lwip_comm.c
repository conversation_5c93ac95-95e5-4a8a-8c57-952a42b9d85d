/************************************************************
 * ??????????????????????????? 
 * ?????lwip_comm.c
 * ????: ?????
 * ??: ?????????????????
 * ???: YunXiang_TechShare  
 * Q Q: 2228398717
 * ???????????
************************************************************/
#include "lwip_comm.h"
#include "netif/etharp.h"
#include "lwip/dhcp.h"
#include "lwip/mem.h"
#include "lwip/memp.h"
#include "lwip/init.h"
#include "ethernetif.h"
#include "lwip/timers.h"
#include "lwip/tcp_impl.h"
#include "lwip/ip_frag.h"
#include "lwip/tcpip.h"
#include "malloc.h"
#include <stdio.h>
#include "lan8720.h"
  
/************************* ???? *************************/

/************************ ???????? ************************/

__lwip_dev lwipdev;						//lwip??????? 
struct netif lwip_netif;				//?????????????????

extern u32 memp_get_memorysize(void);	//??memp.c???????
extern u8_t *memp_memory;				//??memp.c???????.
extern u8_t *ram_heap;					//??mem.c???????.

u32 TCPTimer=0;			//TCP????????
u32 ARPTimer=0;			//ARP????????
u32 lwip_localtime;		//lwip????????????,????:ms

#if LWIP_DHCP
u32 DHCPfineTimer=0;	//DHCP????????????
u32 DHCPcoarseTimer=0;	//DHCP???????????
#endif

/************************ ???????? ************************/  

/************************************************************ 
 * ????:       lwip_comm_mem_malloc(void)
 * ???:       lwip??mem??memp?????????
 * ????:       ??
 * ???:       ??
 * ?????:     0,???   ????,???
 * ????        ?????
 * ????:       ??
************************************************************/

u8 lwip_comm_mem_malloc(void)
{
	u32 mempsize;
	u32 ramheapsize; 
	mempsize=memp_get_memorysize();			//???memp_memory???????
	memp_memory=mymalloc(SRAMIN,mempsize);	//?memp_memory???????
	ramheapsize=LWIP_MEM_ALIGN_SIZE(MEM_SIZE)+2*LWIP_MEM_ALIGN_SIZE(4*3)+MEM_ALIGNMENT;//???ram heap????
	ram_heap=mymalloc(SRAMIN,ramheapsize);	//?ram_heap??????? 
	if(!memp_memory||!ram_heap)//??????????
	{
		lwip_comm_mem_free();
		return 1;
	}
	return 0;	
}

/************************************************************ 
 * ????:       lwip_comm_mem_free(void)
 * ???:       lwip??mem??memp??????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void lwip_comm_mem_free(void)
{ 	
	myfree(SRAMIN,memp_memory);
	myfree(SRAMIN,ram_heap);
}

/************************************************************ 
 * ????:       lwip_comm_default_ip_set(__lwip_dev *lwipx)
 * ???:       lwip ???IP????
 * ????:       lwipx:lwip??????????
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void lwip_comm_default_ip_set(__lwip_dev *lwipx)
{
	u32 sn0;
	sn0=*(vu32*)(0x1FFFF7F0);//???STM32?????ID???24?????MAC??????????
	//Remote server IP: ***********00
	lwipx->remoteip[0]=192;
	lwipx->remoteip[1]=168;
	lwipx->remoteip[2]=6;
	lwipx->remoteip[3]=100;
	//MAC???????(??????????:2.0.0,?????????STM32???ID)
	lwipx->mac[0]=2;//???????(IEEE??????????ID,OUI)???????:2.0.0
	lwipx->mac[1]=0;
	lwipx->mac[2]=0;
	lwipx->mac[3]=(sn0>>16)&0XFF;//?????????STM32?????ID
	lwipx->mac[4]=(sn0>>8)&0XFFF;;
	lwipx->mac[5]=sn0&0XFF; 
	//Local device IP: ************
	lwipx->ip[0]=192;
	lwipx->ip[1]=168;
	lwipx->ip[2]=6;
	lwipx->ip[3]=30;
	//???????????:*************
	lwipx->netmask[0]=255;	
	lwipx->netmask[1]=255;
	lwipx->netmask[2]=255;
	lwipx->netmask[3]=0;
	//Gateway IP: ***********
	lwipx->gateway[0]=192;
	lwipx->gateway[1]=168;
	lwipx->gateway[2]=6;
	lwipx->gateway[3]=1;
	lwipx->dhcpstatus=0;//???DHCP	
} 

/************************************************************ 
 * ????:       lwip_comm_init(void)
 * ???:       LWIP?????(LWIP????????????)
 * ????:       ??
 * ???:       ??
 * ?????:    
							0,???
							1,??????
							2,LAN8720????????
							3,???????????.
 * ????        ?????
 * ????:       ??
************************************************************/

u8 lwip_comm_init(void)
{
	struct netif *Netif_Init_Flag;		//????netif_add()???????????,?????????????????????
	struct ip_addr ipaddr;  			//ip???
	struct ip_addr netmask; 			//????????
	struct ip_addr gw;      			//???????

	printf("\r\n=== LWIP????????????????? ===\r\n");

	printf("[LWIP] ????ETH???...\r\n");
	if(ETH_Mem_Malloc()) {
		printf("[LWIP] ETH??????????\r\n");
		return 1;		//??????????
	}
	printf("[LWIP] ETH????????\r\n");

	printf("[LWIP] ????LWIP???...\r\n");
	if(lwip_comm_mem_malloc()) {
		printf("[LWIP] LWIP??????????\r\n");
		return 1;	//??????????
	}
	printf("[LWIP] LWIP????????\r\n");

	printf("[LWIP] ?????LAN8720???????...\r\n");
	if(LAN8720_Init()) {
		printf("[LWIP] LAN8720?????????\r\n");
		return 2;			//?????LAN8720???
	}
	printf("[LWIP] LAN8720????????\r\n");

	printf("[LWIP] ?????LWIP?????????...\r\n");
	lwip_init();						//?????LWIP???
	printf("[LWIP] LWIP????????????????\r\n");

	lwip_comm_default_ip_set(&lwipdev);	//???????IP?????
	printf("[LWIP] ???IP???????\r\n");

#if LWIP_DHCP		//?????IP
	printf("[LWIP] ?????DHCP??????IP??\r\n");
	ipaddr.addr = 0;
	netmask.addr = 0;
	gw.addr = 0;
#else				//?????IP
	printf("[LWIP] ????????IP??\r\n");
	IP4_ADDR(&ipaddr,lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);
	IP4_ADDR(&netmask,lwipdev.netmask[0],lwipdev.netmask[1] ,lwipdev.netmask[2],lwipdev.netmask[3]);
	IP4_ADDR(&gw,lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);
	printf("????MAC????:................%d.%d.%d.%d.%d.%d\r\n",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);
	printf("???IP???........................%d.%d.%d.%d\r\n",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);
	printf("????????..........................%d.%d.%d.%d\r\n",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);
	printf("???????..........................%d.%d.%d.%d\r\n",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);
#endif
	printf("[LWIP] ????????????????...\r\n");
	Netif_Init_Flag=netif_add(&lwip_netif,&ipaddr,&netmask,&gw,NULL,&ethernetif_init,&ethernet_input);//???????????????????????

#if LWIP_DHCP			//??????DHCP???
	printf("[LWIP] ????DHCP?????...\r\n");
	lwipdev.dhcpstatus=0;	//DHCP????0
	dhcp_start(&lwip_netif);	//????DHCP????
	printf("[LWIP] DHCP???????????????????IP???...\r\n");
#endif

	if(Netif_Init_Flag==NULL) {
		printf("[LWIP] ??????????????\r\n");
		return 3;//???????????
	} else {//????????????,????netif?????,?????netif????
		printf("[LWIP] ????????????\r\n");
		netif_set_default(&lwip_netif); //????netif????????
		netif_set_up(&lwip_netif);		//??netif????
		printf("[LWIP] ????????????\r\n");
	}
	printf("=== LWIP????????????????? ===\r\n\r\n");
	return 0;//????OK.
}   

/************************************************************ 
 * ????:       lwip_pkt_handle(void)
 * ???:       ??????????????? 
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void lwip_pkt_handle(void)
{
  //????????????????????????????????????LWIP???? 
 ethernetif_input(&lwip_netif);
}
/************************************************************ 
 * ????:       lwip_periodic_handle()
 * ???:       LWIP???????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void lwip_periodic_handle()
{
    static uint32_t mem_check_timer = 0;

#if LWIP_TCP
	//?250ms???????tcp_tmr()????
  if (lwip_localtime - TCPTimer >= TCP_TMR_INTERVAL)
  {
    TCPTimer =  lwip_localtime;
    tcp_tmr();
  }
#endif
  //ARP?5s???????
  if ((lwip_localtime - ARPTimer) >= ARP_TMR_INTERVAL)
  {
    ARPTimer =  lwip_localtime;
    etharp_tmr();
  }

  // ?60????????????????
  if ((lwip_localtime - mem_check_timer) >= 60000)
  {
    u32 sramin_used, sramccm_used;

    mem_check_timer = lwip_localtime;

    // ????????????
    sramin_used = my_mem_perused(SRAMIN);
    sramccm_used = my_mem_perused(SRAMCCM);

    printf("[MEM] ????????? - SRAMIN: %ld%%, SRAMCCM: %ld%%\r\n",
           sramin_used, sramccm_used);

    if(sramin_used > 80 || sramccm_used > 80) {
        printf("[WARNING] ????????????\r\n");
    }
  }

#if LWIP_DHCP //??????DHCP???
  //?500ms???????dhcp_fine_tmr()
  if (lwip_localtime - DHCPfineTimer >= DHCP_FINE_TIMER_MSECS)
  {
    DHCPfineTimer =  lwip_localtime;
    dhcp_fine_tmr();
    if ((lwipdev.dhcpstatus != 2)&&(lwipdev.dhcpstatus != 0XFF))
    { 
      lwip_dhcp_process_handle();  //DHCP????
    }
  }

  //?60s??????DHCP??????
  if (lwip_localtime - DHCPcoarseTimer >= DHCP_COARSE_TIMER_MSECS)
  {
    DHCPcoarseTimer =  lwip_localtime;
    dhcp_coarse_tmr();
  }  
#endif
}

/************************************************************ 
 * ????:       lwip_dhcp_process_handle(void)
 * ???:       DHCP????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/
//????????DHCP
#if LWIP_DHCP

void lwip_dhcp_process_handle(void)
{
	u32 ip=0,netmask=0,gw=0;
	switch(lwipdev.dhcpstatus)
	{
		case 0: 	//????DHCP
			printf("[DHCP] ???????DHCP??????...\r\n");
			dhcp_start(&lwip_netif);
			lwipdev.dhcpstatus = 1;		//??????DHCP?????????
			printf("[DHCP] DHCP???????????????????...\r\n");
			break;
		case 1:		//????????IP???
		{
			ip=lwip_netif.ip_addr.addr;		//?????IP???
			netmask=lwip_netif.netmask.addr;//???????????
			gw=lwip_netif.gw.addr;			//?????????? 
			
			if(ip!=0)			//????????IP????????
			{
				lwipdev.dhcpstatus=2;	//DHCP???
				printf("\r\n=== DHCP???IP?????? ===\r\n");
				printf("[DHCP] ????MAC????:................%d.%d.%d.%d.%d.%d\r\n",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);
				//?????????DHCP???????IP???
				lwipdev.ip[3]=(uint8_t)(ip>>24);
				lwipdev.ip[2]=(uint8_t)(ip>>16);
				lwipdev.ip[1]=(uint8_t)(ip>>8);
				lwipdev.ip[0]=(uint8_t)(ip);
				printf("[DHCP] ???DHCP?????IP???..............%d.%d.%d.%d\r\n",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);
				//???????DHCP?????????????????
				lwipdev.netmask[3]=(uint8_t)(netmask>>24);
				lwipdev.netmask[2]=(uint8_t)(netmask>>16);
				lwipdev.netmask[1]=(uint8_t)(netmask>>8);
				lwipdev.netmask[0]=(uint8_t)(netmask);
				printf("[DHCP] ???DHCP?????????????............%d.%d.%d.%d\r\n",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);
				//?????????DHCP??????????????
				lwipdev.gateway[3]=(uint8_t)(gw>>24);
				lwipdev.gateway[2]=(uint8_t)(gw>>16);
				lwipdev.gateway[1]=(uint8_t)(gw>>8);
				lwipdev.gateway[0]=(uint8_t)(gw);
				printf("[DHCP] ???DHCP????????????..........%d.%d.%d.%d\r\n",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);
				printf("=== ?????????????????????????? ===\r\n\r\n");
			}else if(lwip_netif.dhcp->tries>LWIP_MAX_DHCP_TRIES) //???DHCP??????IP??????,?????????????
			{
				lwipdev.dhcpstatus=0XFF;//DHCP??????.
				printf("\r\n=== DHCP???IP?????? ===\r\n");
				printf("[DHCP] DHCP????????????????????????IP??\r\n");
				//?????IP???
				IP4_ADDR(&(lwip_netif.ip_addr),lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);
				IP4_ADDR(&(lwip_netif.netmask),lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);
				IP4_ADDR(&(lwip_netif.gw),lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);
				printf("[DHCP] DHCP?????????????IP???!\r\n");
				printf("[DHCP] ????MAC????:................%d.%d.%d.%d.%d.%d\r\n",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);
				printf("[DHCP] ???IP???........................%d.%d.%d.%d\r\n",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);
				printf("[DHCP] ????????..........................%d.%d.%d.%d\r\n",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);
				printf("[DHCP] ???????..........................%d.%d.%d.%d\r\n",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);
				printf("=== ???????????????IP????===\r\n\r\n");
			}
		}
		break;
		default : break;
	}
}
#endif 



/************************************************************
 * ????:       lwip_get_network_stats(void)
 * ???:       ?????????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/
void lwip_get_network_stats(void)
{
    u32 sramin_used, sramccm_used;

    printf("\r\n=== ����״̬ͳ�� ===\r\n");
    printf("[STATS] ???IP???: %d.%d.%d.%d\r\n",
           lwipdev.ip[0], lwipdev.ip[1], lwipdev.ip[2], lwipdev.ip[3]);
    printf("[STATS] ????????: %d.%d.%d.%d\r\n",
           lwipdev.netmask[0], lwipdev.netmask[1], lwipdev.netmask[2], lwipdev.netmask[3]);
    printf("[STATS] ???????: %d.%d.%d.%d\r\n",
           lwipdev.gateway[0], lwipdev.gateway[1], lwipdev.gateway[2], lwipdev.gateway[3]);
    printf("[STATS] MAC???: %02X:%02X:%02X:%02X:%02X:%02X\r\n",
           lwipdev.mac[0], lwipdev.mac[1], lwipdev.mac[2],
           lwipdev.mac[3], lwipdev.mac[4], lwipdev.mac[5]);

    #if LWIP_DHCP
    printf("[STATS] DHCP??: ");
    switch(lwipdev.dhcpstatus) {
        case 0: printf("??????IP???\r\n"); break;
        case 1: printf("???DHCP???\r\n"); break;
        case 2: printf("????IP???\r\n"); break;
        case 0xFF: printf("??????????IP\r\n"); break;
        default: printf("?????\r\n"); break;
    }
    #endif

    printf("[STATS] ��·״̬: %s\r\n", LAN8720_Get_Link_Status() ? "������" : "δ����");

    // ��ȡ�ڴ�ʹ�����
    sramin_used = my_mem_perused(SRAMIN);
    sramccm_used = my_mem_perused(SRAMCCM);
    printf("[STATS] ??????: SRAMIN %ld%%, SRAMCCM %ld%%\r\n",
           sramin_used, sramccm_used);

    printf("===================\r\n\r\n");
}

/****************************End*****************************/



