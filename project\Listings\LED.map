Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to implement.o(.text) for System_Init
    systick.o(.text) refers to gd32f4xx_misc.o(.text) for systick_clksource_set
    systick.o(.text) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    systick.o(.text) refers to systick.o(.data) for .data
    led.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    led.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_mode_set
    key.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    key.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_mode_set
    lan8720.o(.text) refers to gd32f4xx_enet.o(.text) for enet_phy_write_read
    lan8720.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    lan8720.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    lan8720.o(.text) refers to systick.o(.text) for delay_1ms
    lan8720.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_mode_set
    lan8720.o(.text) refers to gd32f4xx_syscfg.o(.text) for syscfg_enet_phy_interface_config
    lan8720.o(.text) refers to gd32f4xx_misc.o(.text) for nvic_vector_table_set
    lan8720.o(.text) refers to lwip_comm.o(.text) for lwip_pkt_handle
    lan8720.o(.text) refers to malloc.o(.text) for myfree
    lan8720.o(.text) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    lan8720.o(.text) refers to lan8720.o(.conststring) for .conststring
    lan8720.o(.text) refers to lan8720.o(.data) for .data
    lan8720.o(.text) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    lcd.o(.text) refers to systick.o(.text) for delay_1us
    lcd.o(.text) refers to lcd.o(.bss) for .bss
    lcd.o(.text) refers to dfltui.o(.text) for __aeabi_ui2d
    lcd.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    lcd.o(.text) refers to dfixui.o(.text) for __aeabi_d2uiz
    lcd.o(.text) refers to lcd.o(.data) for .data
    lcd.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    lcd.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_mode_set
    lcd.o(.text) refers to gd32f4xx_exmc.o(.text) for exmc_norsram_init
    lcd.o(.text) refers to lcd.o(.constdata) for .constdata
    usart0.o(.text) refers to gd32f4xx_usart.o(.text) for usart_data_transmit
    usart0.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    usart0.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_af_set
    usart0.o(.text) refers to usart0.o(.data) for .data
    implement.o(.text) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    implement.o(.text) refers to lcd.o(.text) for LCD_ShowString
    implement.o(.text) refers to systick.o(.text) for systick_config
    implement.o(.text) refers to gd32f4xx_misc.o(.text) for nvic_priority_group_set
    implement.o(.text) refers to usart0.o(.text) for USART0_Config
    implement.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    implement.o(.text) refers to led.o(.text) for LED_Init
    implement.o(.text) refers to key.o(.text) for KEY_Init
    implement.o(.text) refers to malloc.o(.text) for my_mem_init
    implement.o(.text) refers to timer.o(.text) for Timer3_Init
    implement.o(.text) refers to lwip_comm.o(.bss) for lwipdev
    implement.o(.text) refers to lcd.o(.data) for POINT_COLOR
    implement.o(.text) refers to lwip_comm.o(.text) for lwip_comm_init
    implement.o(.text) refers to lan8720.o(.text) for LAN8720_Get_Link_Status
    implement.o(.text) refers to lwip_comm.o(.data) for lwip_localtime
    implement.o(.text) refers to implement.o(.data) for .data
    timer.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    timer.o(.text) refers to gd32f4xx_timer.o(.text) for timer_deinit
    timer.o(.text) refers to gd32f4xx_misc.o(.text) for nvic_irq_enable
    timer.o(.text) refers to lwip_comm.o(.data) for lwip_localtime
    system_gd32f4xx.o(.text) refers to system_gd32f4xx.o(.data) for .data
    gd32f4xx_adc.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_can.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_can.o(.text) refers to gd32f4xx_dbg.o(.text) for dbg_periph_enable
    gd32f4xx_ctc.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_dac.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_dci.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_enet.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_enet.o(.text) refers to gd32f4xx_enet.o(.bss) for .bss
    gd32f4xx_enet.o(.text) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(.text) refers to gd32f4xx_enet.o(.constdata) for .constdata
    gd32f4xx_gpio.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_iref.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(.text) refers to gd32f4xx_pmu.o(.bss) for .bss
    gd32f4xx_sdio.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_spi.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_timer.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_tli.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_trng.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_usart.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(.text) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to timer.o(.text) for TIMER3_IRQHandler
    startup_gd32f450_470.o(RESET) refers to usart0.o(.text) for USART0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to lan8720.o(.text) for ENET_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(.text) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    lwip_comm.o(.text) refers to malloc.o(.text) for myfree
    lwip_comm.o(.text) refers to memp.o(.text) for memp_get_memorysize
    lwip_comm.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    lwip_comm.o(.text) refers to lan8720.o(.text) for ETH_Mem_Malloc
    lwip_comm.o(.text) refers to init.o(.text) for lwip_init
    lwip_comm.o(.text) refers to netif.o(.text) for netif_add
    lwip_comm.o(.text) refers to dhcp.o(.text) for dhcp_start
    lwip_comm.o(.text) refers to ethernetif.o(.text) for ethernetif_input
    lwip_comm.o(.text) refers to memp.o(.data) for memp_memory
    lwip_comm.o(.text) refers to mem.o(.data) for ram_heap
    lwip_comm.o(.text) refers to lwip_comm.o(.bss) for .bss
    lwip_comm.o(.text) refers to etharp.o(.text) for ethernet_input
    lwip_comm.o(.text) refers to tcp.o(.text) for tcp_tmr
    lwip_comm.o(.text) refers to lwip_comm.o(.data) for .data
    etharp.o(.text) refers to pbuf.o(.text) for pbuf_free
    etharp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    etharp.o(.text) refers to ip_addr.o(.text) for ip4_addr_isbroadcast
    etharp.o(.text) refers to def.o(.text) for lwip_htons
    etharp.o(.text) refers to etharp.o(.bss) for .bss
    etharp.o(.text) refers to etharp.o(.conststring) for .conststring
    etharp.o(.text) refers to dhcp.o(.text) for dhcp_arp_reply
    etharp.o(.text) refers to memcmp.o(.text) for memcmp
    etharp.o(.text) refers to etharp.o(.constdata) for .constdata
    etharp.o(.text) refers to etharp.o(.data) for .data
    etharp.o(.text) refers to ip.o(.text) for ip_input
    ethernetif.o(.text) refers to lan8720.o(.text) for ETH_GetCurrentTxBuffer
    ethernetif.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    ethernetif.o(.text) refers to pbuf.o(.text) for pbuf_alloc
    ethernetif.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    ethernetif.o(.text) refers to gd32f4xx_enet.o(.text) for enet_mac_address_set
    ethernetif.o(.text) refers to etharp.o(.text) for etharp_output
    ethernetif.o(.text) refers to lwip_comm.o(.bss) for lwipdev
    ethernetif.o(.text) refers to gd32f4xx_enet.o(.data) for txdesc_tab
    icmp.o(.text) refers to pbuf.o(.text) for pbuf_header
    icmp.o(.text) refers to ip_addr.o(.text) for ip4_addr_isbroadcast
    icmp.o(.text) refers to inet_chksum.o(.text) for inet_chksum_pbuf
    icmp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    icmp.o(.text) refers to ip.o(.text) for ip_output_if
    icmp.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    icmp.o(.text) refers to ip.o(.data) for current_iphdr_dest
    inet_chksum.o(.text) refers to def.o(.text) for lwip_htons
    inet_chksum.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    ip.o(.text) refers to def.o(.text) for lwip_ntohs
    ip.o(.text) refers to pbuf.o(.text) for pbuf_free
    ip.o(.text) refers to ip_addr.o(.text) for ip4_addr_isbroadcast
    ip.o(.text) refers to ip_frag.o(.text) for ip_reass
    ip.o(.text) refers to raw.o(.text) for raw_input
    ip.o(.text) refers to udp.o(.text) for udp_input
    ip.o(.text) refers to tcp_in.o(.text) for tcp_input
    ip.o(.text) refers to icmp.o(.text) for icmp_input
    ip.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    ip.o(.text) refers to netif.o(.data) for netif_list
    ip.o(.text) refers to ip.o(.data) for .data
    ip_addr.o(.text) refers to def.o(.text) for lwip_htonl
    ip_addr.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    ip_addr.o(.text) refers to ip_addr.o(.bss) for .bss
    ip_frag.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    ip_frag.o(.text) refers to memp.o(.text) for memp_free
    ip_frag.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    ip_frag.o(.text) refers to icmp.o(.text) for icmp_time_exceeded
    ip_frag.o(.text) refers to pbuf.o(.text) for pbuf_clen
    ip_frag.o(.text) refers to def.o(.text) for lwip_ntohs
    ip_frag.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    ip_frag.o(.text) refers to ip_frag.o(.data) for .data
    ip_frag.o(.text) refers to inet_chksum.o(.text) for inet_chksum
    dhcp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    dhcp.o(.text) refers to pbuf.o(.text) for pbuf_free
    dhcp.o(.text) refers to def.o(.text) for lwip_htonl
    dhcp.o(.text) refers to udp.o(.text) for udp_sendto_if
    dhcp.o(.text) refers to dhcp.o(.conststring) for .conststring
    dhcp.o(.text) refers to dhcp.o(.data) for .data
    dhcp.o(.text) refers to ip_addr.o(.constdata) for ip_addr_broadcast
    dhcp.o(.text) refers to etharp.o(.text) for etharp_query
    dhcp.o(.text) refers to netif.o(.text) for netif_set_down
    dhcp.o(.text) refers to netif.o(.data) for netif_list
    dhcp.o(.text) refers to ip_addr.o(.constdata) for ip_addr_any
    dhcp.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    dhcp.o(.text) refers to mem.o(.text) for mem_free
    dhcp.o(.text) refers to dhcp.o(.bss) for .bss
    init.o(.text) refers to mem.o(.text) for mem_init
    init.o(.text) refers to memp.o(.text) for memp_init
    init.o(.text) refers to netif.o(.text) for netif_init
    init.o(.text) refers to udp.o(.text) for udp_init
    init.o(.text) refers to tcp.o(.text) for tcp_init
    init.o(.text) refers to timers.o(.text) for sys_timeouts_init
    mem.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    mem.o(.text) refers to memseta.o(.text) for __aeabi_memclr
    mem.o(.text) refers to mem.o(.data) for .data
    memp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    memp.o(.text) refers to memp.o(.data) for .data
    memp.o(.text) refers to memp.o(.bss) for .bss
    memp.o(.text) refers to memp.o(.constdata) for .constdata
    netif.o(.text) refers to tcp.o(.text) for tcp_abort
    netif.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    netif.o(.text) refers to etharp.o(.text) for etharp_cleanup_netif
    netif.o(.text) refers to dhcp.o(.text) for dhcp_network_changed
    netif.o(.text) refers to tcp.o(.data) for tcp_active_pcbs
    netif.o(.text) refers to netif.o(.data) for .data
    pbuf.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    pbuf.o(.text) refers to mem.o(.text) for mem_free
    pbuf.o(.text) refers to memp.o(.text) for memp_free
    pbuf.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    pbuf.o(.text) refers to strlen.o(.text) for strlen
    raw.o(.text) refers to pbuf.o(.text) for pbuf_header
    raw.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    raw.o(.text) refers to ip.o(.text) for ip_route
    raw.o(.text) refers to memp.o(.text) for memp_free
    raw.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    raw.o(.text) refers to raw.o(.data) for .data
    raw.o(.text) refers to ip.o(.data) for current_iphdr_dest
    tcp.o(.text) refers to pbuf.o(.text) for pbuf_free
    tcp.o(.text) refers to memp.o(.text) for memp_free
    tcp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    tcp.o(.text) refers to tcp_out.o(.text) for tcp_zero_window_probe
    tcp.o(.text) refers to tcp.o(.data) for .data
    tcp.o(.text) refers to tcp.o(.constdata) for .constdata
    tcp.o(.text) refers to timers.o(.text) for tcp_timer_needed
    tcp.o(.text) refers to ip.o(.text) for ip_route
    tcp.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    tcp.o(.constdata) refers to tcp.o(.conststring) for .conststring
    tcp.o(.constdata) refers to tcp.o(.data) for tcp_listen_pcbs
    tcp_in.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    tcp_in.o(.text) refers to tcp_out.o(.text) for tcp_rexmit_fast
    tcp_in.o(.text) refers to pbuf.o(.text) for pbuf_clen
    tcp_in.o(.text) refers to def.o(.text) for lwip_ntohs
    tcp_in.o(.text) refers to tcp.o(.text) for tcp_seg_free
    tcp_in.o(.text) refers to tcp_in.o(.data) for .data
    tcp_in.o(.text) refers to tcp.o(.data) for tcp_ticks
    tcp_in.o(.text) refers to tcp_in.o(.bss) for .bss
    tcp_in.o(.text) refers to ip.o(.data) for current_iphdr_src
    tcp_in.o(.text) refers to timers.o(.text) for tcp_timer_needed
    tcp_in.o(.text) refers to ip_addr.o(.text) for ip4_addr_isbroadcast
    tcp_in.o(.text) refers to memp.o(.text) for memp_free
    tcp_out.o(.text) refers to pbuf.o(.text) for pbuf_alloc
    tcp_out.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    tcp_out.o(.text) refers to def.o(.text) for lwip_htons
    tcp_out.o(.text) refers to memp.o(.text) for memp_malloc
    tcp_out.o(.text) refers to tcp.o(.text) for tcp_seg_free
    tcp_out.o(.text) refers to tcp_out.o(.conststring) for .conststring
    tcp_out.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    tcp_out.o(.text) refers to ip.o(.text) for ip_output
    tcp_out.o(.text) refers to tcp.o(.data) for tcp_ticks
    tcp_out.o(.text) refers to tcp_in.o(.data) for tcp_input_pcb
    timers.o(.text) refers to memp.o(.text) for memp_malloc
    timers.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    timers.o(.text) refers to tcp.o(.text) for tcp_tmr
    timers.o(.text) refers to ip_frag.o(.text) for ip_reass_tmr
    timers.o(.text) refers to etharp.o(.text) for etharp_tmr
    timers.o(.text) refers to dhcp.o(.text) for dhcp_coarse_tmr
    timers.o(.text) refers to sys_arch.o(.text) for sys_now
    timers.o(.text) refers to timers.o(.data) for .data
    timers.o(.text) refers to tcp.o(.data) for tcp_active_pcbs
    udp.o(.text) refers to pbuf.o(.text) for pbuf_header
    udp.o(.text) refers to ip_addr.o(.text) for ip4_addr_isbroadcast
    udp.o(.text) refers to def.o(.text) for lwip_ntohs
    udp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    udp.o(.text) refers to icmp.o(.text) for icmp_dest_unreach
    udp.o(.text) refers to ip.o(.text) for ip_output_if
    udp.o(.text) refers to memp.o(.text) for memp_free
    udp.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    udp.o(.text) refers to ip.o(.data) for current_iphdr_dest
    udp.o(.text) refers to udp.o(.data) for .data
    sys_arch.o(.text) refers to lwip_comm.o(.data) for lwip_localtime
    malloc.o(.text) refers to malloc.o(.constdata) for .constdata
    malloc.o(.text) refers to malloc.o(.data) for .data
    malloc.o(.data) refers to malloc.o(.text) for my_mem_init
    malloc.o(.data) refers to malloc.o(.bss) for mem1base
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x68000000) for mem2base
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x10000000) for mem3base
    malloc.o(.data) refers to malloc.o(.bss) for mem1mapbase
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x68032000) for mem2mapbase
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x1000F000) for mem3mapbase
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart0.o(.text) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart0.o(.text) for fputc
    printfb.o(i.__0printf$bare) refers to usart0.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart0.o(.text) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart0.o(.text) for fputc
    printfb.o(i.__0vprintf$bare) refers to usart0.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart0.o(.text) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart0.o(.text) for fputc
    printf0.o(i.__0printf$0) refers to usart0.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart0.o(.text) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart0.o(.text) for fputc
    printf0.o(i.__0vprintf$0) refers to usart0.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart0.o(.text) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart0.o(.text) for fputc
    printf1.o(i.__0printf$1) refers to usart0.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart0.o(.text) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart0.o(.text) for fputc
    printf1.o(i.__0vprintf$1) refers to usart0.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart0.o(.text) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart0.o(.text) for fputc
    printf2.o(i.__0printf$2) refers to usart0.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart0.o(.text) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart0.o(.text) for fputc
    printf2.o(i.__0vprintf$2) refers to usart0.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart0.o(.text) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart0.o(.text) for fputc
    printf3.o(i.__0printf$3) refers to usart0.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart0.o(.text) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart0.o(.text) for fputc
    printf3.o(i.__0vprintf$3) refers to usart0.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart0.o(.text) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart0.o(.text) for fputc
    printf4.o(i.__0printf$4) refers to usart0.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart0.o(.text) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart0.o(.text) for fputc
    printf4.o(i.__0vprintf$4) refers to usart0.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart0.o(.text) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart0.o(.text) for fputc
    printf5.o(i.__0printf$5) refers to usart0.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart0.o(.text) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart0.o(.text) for fputc
    printf5.o(i.__0vprintf$5) refers to usart0.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart0.o(.text) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart0.o(.text) for fputc
    printf6.o(i.__0printf$6) refers to usart0.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart0.o(.text) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart0.o(.text) for fputc
    printf6.o(i.__0vprintf$6) refers to usart0.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart0.o(.text) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart0.o(.text) for fputc
    printf7.o(i.__0printf$7) refers to usart0.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart0.o(.text) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart0.o(.text) for fputc
    printf7.o(i.__0vprintf$7) refers to usart0.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart0.o(.text) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart0.o(.text) for fputc
    printf8.o(i.__0printf$8) refers to usart0.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart0.o(.text) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart0.o(.text) for fputc
    printf8.o(i.__0vprintf$8) refers to usart0.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart0.o(.text) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart0.o(.text) for fputc
    printfa.o(i.__0printf) refers to usart0.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart0.o(.text) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart0.o(.text) for fputc
    printfa.o(i.__0vprintf) refers to usart0.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing lan8720.o(.rev16_text), (4 bytes).
    Removing lan8720.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing usart0.o(.rev16_text), (4 bytes).
    Removing usart0.o(.revsh_text), (4 bytes).
    Removing implement.o(.rev16_text), (4 bytes).
    Removing implement.o(.revsh_text), (4 bytes).
    Removing implement.o(.data), (2 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.text), (1440 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(.text), (1668 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(.text), (88 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.text), (404 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(.text), (816 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.text), (108 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(.text), (308 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(.text), (1292 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(.text), (256 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.text), (1364 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.text), (136 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.text), (808 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.text), (936 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(.text), (124 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.text), (560 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.text), (2180 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.text), (740 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(.text), (904 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.text), (856 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(.text), (132 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.text), (140 bytes).
    Removing startup_gd32f450_470.o(HEAP), (1024 bytes).
    Removing lwip_comm.o(.rev16_text), (4 bytes).
    Removing lwip_comm.o(.revsh_text), (4 bytes).
    Removing ethernetif.o(.rev16_text), (4 bytes).
    Removing ethernetif.o(.revsh_text), (4 bytes).
    Removing sys_arch.o(.rev16_text), (4 bytes).
    Removing sys_arch.o(.revsh_text), (4 bytes).
    Removing malloc.o(.rev16_text), (4 bytes).
    Removing malloc.o(.revsh_text), (4 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).

116 unused section(s) (total 17306 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\CMSIS\system_gd32f4xx.c               0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\HardWare\KEY\KEY.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HardWare\LAN8720\lan8720.c            0x00000000   Number         0  lan8720.o ABSOLUTE
    ..\HardWare\LCD\LCD.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HardWare\LED\LED.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\Implement\Implement.c                 0x00000000   Number         0  implement.o ABSOLUTE
    ..\LWIP\arch\sys_arch.c                  0x00000000   Number         0  sys_arch.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\api_lib.c     0x00000000   Number         0  api_lib.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\api_msg.c     0x00000000   Number         0  api_msg.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\err.c         0x00000000   Number         0  err.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\netbuf.c      0x00000000   Number         0  netbuf.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\netdb.c       0x00000000   Number         0  netdb.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\netifapi.c    0x00000000   Number         0  netifapi.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\sockets.c     0x00000000   Number         0  sockets.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\tcpip.c       0x00000000   Number         0  tcpip.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\def.c        0x00000000   Number         0  def.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\dhcp.c       0x00000000   Number         0  dhcp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\dns.c        0x00000000   Number         0  dns.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\init.c       0x00000000   Number         0  init.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\autoip.c 0x00000000   Number         0  autoip.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\icmp.c  0x00000000   Number         0  icmp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\igmp.c  0x00000000   Number         0  igmp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\inet.c  0x00000000   Number         0  inet.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\inet_chksum.c 0x00000000   Number         0  inet_chksum.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\ip.c    0x00000000   Number         0  ip.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\ip_addr.c 0x00000000   Number         0  ip_addr.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\ip_frag.c 0x00000000   Number         0  ip_frag.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\lwip_sys.c   0x00000000   Number         0  lwip_sys.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\mem.c        0x00000000   Number         0  mem.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\memp.c       0x00000000   Number         0  memp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\netif.c      0x00000000   Number         0  netif.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\pbuf.c       0x00000000   Number         0  pbuf.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\raw.c        0x00000000   Number         0  raw.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\stats.c      0x00000000   Number         0  stats.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\tcp.c        0x00000000   Number         0  tcp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\tcp_in.c     0x00000000   Number         0  tcp_in.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\tcp_out.c    0x00000000   Number         0  tcp_out.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\timers.c     0x00000000   Number         0  timers.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\udp.c        0x00000000   Number         0  udp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\netif\etharp.c    0x00000000   Number         0  etharp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\netif\ethernetif.c 0x00000000   Number         0  ethernetif.o ABSOLUTE
    ..\LWIP\lwip_app\lwip_comm\lwip_comm.c   0x00000000   Number         0  lwip_comm.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\MALLOC\malloc.c                       0x00000000   Number         0  malloc.o ABSOLUTE
    ..\Protocol\USART0\USART0.c              0x00000000   Number         0  usart0.o ABSOLUTE
    ..\Startup\startup_gd32f450_470.s        0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    ..\System\TIMER\TIMER.c                  0x00000000   Number         0  timer.o ABSOLUTE
    ..\User\gd32f4xx_it.c                    0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\systick.c                        0x00000000   Number         0  systick.o ABSOLUTE
    ..\\CMSIS\\system_gd32f4xx.c             0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\\HardWare\\KEY\\KEY.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HardWare\\LAN8720\\lan8720.c         0x00000000   Number         0  lan8720.o ABSOLUTE
    ..\\HardWare\\LCD\\LCD.c                 0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\HardWare\\LED\\LED.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\Implement\\Implement.c               0x00000000   Number         0  implement.o ABSOLUTE
    ..\\LWIP\\arch\\sys_arch.c               0x00000000   Number         0  sys_arch.o ABSOLUTE
    ..\\LWIP\\lwip-1.4.1\\src\\netif\\ethernetif.c 0x00000000   Number         0  ethernetif.o ABSOLUTE
    ..\\LWIP\\lwip_app\\lwip_comm\\lwip_comm.c 0x00000000   Number         0  lwip_comm.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\\MALLOC\\malloc.c                     0x00000000   Number         0  malloc.o ABSOLUTE
    ..\\Protocol\\USART0\\USART0.c           0x00000000   Number         0  usart0.o ABSOLUTE
    ..\\System\\TIMER\\TIMER.c               0x00000000   Number         0  timer.o ABSOLUTE
    ..\\User\\gd32f4xx_it.c                  0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\\User\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\User\\systick.c                      0x00000000   Number         0  systick.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080001bc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001c0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001c0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001c0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001c0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c4   Section        0  gd32f4xx_it.o(.text)
    .text                                    0x080001d6   Section        0  main.o(.text)
    .text                                    0x080001e4   Section        0  systick.o(.text)
    .text                                    0x080002b0   Section        0  led.o(.text)
    .text                                    0x080002ec   Section        0  key.o(.text)
    .text                                    0x08000360   Section        0  lan8720.o(.text)
    .text                                    0x08000fac   Section        0  lcd.o(.text)
    .text                                    0x08004bd4   Section        0  usart0.o(.text)
    .text                                    0x08004d10   Section        0  implement.o(.text)
    .text                                    0x080057cc   Section        0  timer.o(.text)
    .text                                    0x08005860   Section        0  system_gd32f4xx.o(.text)
    system_clock_240m_25m_hxtal              0x08005861   Thumb Code   156  system_gd32f4xx.o(.text)
    .text                                    0x08005a1c   Section        0  gd32f4xx_enet.o(.text)
    enet_delay                               0x08006a53   Thumb Code    44  gd32f4xx_enet.o(.text)
    .text                                    0x08006a80   Section        0  gd32f4xx_exmc.o(.text)
    .text                                    0x08007210   Section        0  gd32f4xx_gpio.o(.text)
    .text                                    0x080073a8   Section        0  gd32f4xx_misc.o(.text)
    .text                                    0x080074a8   Section        0  gd32f4xx_rcu.o(.text)
    .text                                    0x08007b28   Section        0  gd32f4xx_syscfg.o(.text)
    .text                                    0x08007be4   Section        0  gd32f4xx_timer.o(.text)
    .text                                    0x08008848   Section        0  gd32f4xx_usart.o(.text)
    .text                                    0x08008ca8   Section       36  startup_gd32f450_470.o(.text)
    $v0                                      0x08008ca8   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x08008ccc   Section        0  lwip_comm.o(.text)
    .text                                    0x0800979c   Section        0  etharp.o(.text)
    etharp_free_entry                        0x0800979d   Thumb Code    32  etharp.o(.text)
    etharp_find_entry                        0x080097ff   Thumb Code   332  etharp.o(.text)
    etharp_send_ip                           0x0800994b   Thumb Code    70  etharp.o(.text)
    etharp_update_arp_entry                  0x08009991   Thumb Code   142  etharp.o(.text)
    etharp_arp_input                         0x08009a93   Thumb Code   590  etharp.o(.text)
    etharp_raw                               0x08009ce1   Thumb Code   234  etharp.o(.text)
    etharp_output_to_arp_index               0x08009deb   Thumb Code    84  etharp.o(.text)
    .text                                    0x0800a23c   Section        0  ethernetif.o(.text)
    low_level_output                         0x0800a23d   Thumb Code    52  ethernetif.o(.text)
    low_level_init                           0x0800a315   Thumb Code   126  ethernetif.o(.text)
    .text                                    0x0800a40c   Section        0  icmp.o(.text)
    icmp_send_response                       0x0800a543   Thumb Code   120  icmp.o(.text)
    .text                                    0x0800a73c   Section        0  inet_chksum.o(.text)
    lwip_standard_chksum                     0x0800a73d   Thumb Code    86  inet_chksum.o(.text)
    .text                                    0x0800a988   Section        0  ip.o(.text)
    .text                                    0x0800ace4   Section        0  ip_addr.o(.text)
    .text                                    0x0800af38   Section        0  ip_frag.o(.text)
    ip_reass_dequeue_datagram                0x0800af39   Thumb Code    52  ip_frag.o(.text)
    ip_reass_free_complete_datagram          0x0800af6d   Thumb Code   202  ip_frag.o(.text)
    ip_reass_remove_oldest_datagram          0x0800b05f   Thumb Code   110  ip_frag.o(.text)
    ip_reass_chain_frag_into_datagram_and_validate 0x0800b0cd   Thumb Code   344  ip_frag.o(.text)
    ip_frag_free_pbuf_custom_ref             0x0800b55d   Thumb Code    36  ip_frag.o(.text)
    ipfrag_free_pbuf_custom                  0x0800b581   Thumb Code    40  ip_frag.o(.text)
    .text                                    0x0800b764   Section        0  def.o(.text)
    .text                                    0x0800b774   Section        0  dhcp.o(.text)
    dhcp_delete_msg                          0x0800b775   Thumb Code    78  dhcp.o(.text)
    dhcp_option_trailer                      0x0800b7c3   Thumb Code   104  dhcp.o(.text)
    dhcp_option_byte                         0x0800b82b   Thumb Code    42  dhcp.o(.text)
    dhcp_option                              0x0800b855   Thumb Code    62  dhcp.o(.text)
    dhcp_option_short                        0x0800b893   Thumb Code    60  dhcp.o(.text)
    dhcp_create_msg                          0x0800b8cf   Thumb Code   372  dhcp.o(.text)
    dhcp_discover                            0x0800ba43   Thumb Code   810  dhcp.o(.text)
    dhcp_check                               0x0800bd6d   Thumb Code    48  dhcp.o(.text)
    dhcp_option_long                         0x0800bd9d   Thumb Code    96  dhcp.o(.text)
    dhcp_select                              0x0800bdfd   Thumb Code   228  dhcp.o(.text)
    dhcp_rebind                              0x0800bf65   Thumb Code   130  dhcp.o(.text)
    dhcp_reboot                              0x0800c041   Thumb Code   156  dhcp.o(.text)
    dhcp_bind                                0x0800c17d   Thumb Code   308  dhcp.o(.text)
    dhcp_parse_reply                         0x0800c3b3   Thumb Code  1000  dhcp.o(.text)
    dhcp_recv                                0x0800c79b   Thumb Code   290  dhcp.o(.text)
    dhcp_set_state                           0x0800cab3   Thumb Code    16  dhcp.o(.text)
    dhcp_timeout                             0x0800cac3   Thumb Code   196  dhcp.o(.text)
    dhcp_handle_ack                          0x0800cb87   Thumb Code    88  dhcp.o(.text)
    dhcp_decline                             0x0800cbdf   Thumb Code   114  dhcp.o(.text)
    .text                                    0x0800cc58   Section        0  init.o(.text)
    .text                                    0x0800cc78   Section        0  mem.o(.text)
    plug_holes                               0x0800cc79   Thumb Code   154  mem.o(.text)
    .text                                    0x0800d1a8   Section        0  memp.o(.text)
    .text                                    0x0800d324   Section        0  netif.o(.text)
    .text                                    0x0800d590   Section        0  pbuf.o(.text)
    .text                                    0x0800e2ac   Section        0  raw.o(.text)
    .text                                    0x0800e4a4   Section        0  tcp.o(.text)
    tcp_close_shutdown                       0x0800e87f   Thumb Code   814  tcp.o(.text)
    tcp_new_port                             0x0800ee6d   Thumb Code   276  tcp.o(.text)
    tcp_accept_null                          0x0800f009   Thumb Code     6  tcp.o(.text)
    .text                                    0x0800f494   Section        0  tcp_in.o(.text)
    tcp_receive                              0x0800f495   Thumb Code  1584  tcp_in.o(.text)
    tcp_parseopt                             0x0800fac5   Thumb Code   124  tcp_in.o(.text)
    tcp_process                              0x0800fb41   Thumb Code  1108  tcp_in.o(.text)
    tcp_listen_input                         0x0800ff95   Thumb Code   208  tcp_in.o(.text)
    tcp_timewait_input                       0x08010521   Thumb Code    92  tcp_in.o(.text)
    .text                                    0x080105b0   Section        0  tcp_out.o(.text)
    tcp_output_alloc_header                  0x080105b1   Thumb Code   128  tcp_out.o(.text)
    tcp_create_segment                       0x08010631   Thumb Code   154  tcp_out.o(.text)
    tcp_pbuf_prealloc                        0x0801082d   Thumb Code   100  tcp_out.o(.text)
    tcp_write_checks                         0x08010891   Thumb Code   112  tcp_out.o(.text)
    tcp_output_segment                       0x08010ecb   Thumb Code   528  tcp_out.o(.text)
    .text                                    0x08011510   Section        0  timers.o(.text)
    tcpip_tcp_timer                          0x08011579   Thumb Code    38  timers.o(.text)
    ip_reass_timer                           0x080115c1   Thumb Code    20  timers.o(.text)
    arp_timer                                0x080115d5   Thumb Code    20  timers.o(.text)
    dhcp_timer_coarse                        0x080115e9   Thumb Code    20  timers.o(.text)
    dhcp_timer_fine                          0x080115fd   Thumb Code    20  timers.o(.text)
    .text                                    0x0801179c   Section        0  udp.o(.text)
    .text                                    0x08011c38   Section        0  sys_arch.o(.text)
    .text                                    0x08011c44   Section        0  malloc.o(.text)
    .text                                    0x08011e0c   Section        0  memcpya.o(.text)
    .text                                    0x08011e30   Section        0  memseta.o(.text)
    .text                                    0x08011e54   Section        0  strlen.o(.text)
    .text                                    0x08011e62   Section        0  memcmp.o(.text)
    .text                                    0x08011e7c   Section        0  dmul.o(.text)
    .text                                    0x08011f60   Section        0  dfltui.o(.text)
    .text                                    0x08011f7a   Section        0  dfixui.o(.text)
    .text                                    0x08011fac   Section        0  uldiv.o(.text)
    .text                                    0x0801200e   Section        0  llushr.o(.text)
    .text                                    0x0801202e   Section        0  depilogue.o(.text)
    .text                                    0x0801202e   Section        0  iusefp.o(.text)
    .text                                    0x080120e8   Section       36  init.o(.text)
    .text                                    0x0801210c   Section        0  llshl.o(.text)
    i.__0printf$8                            0x0801212c   Section        0  printf8.o(i.__0printf$8)
    i.__0sprintf$8                           0x0801214c   Section        0  printf8.o(i.__0sprintf$8)
    i.__scatterload_copy                     0x08012174   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08012182   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08012184   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x08012194   Section        0  printf8.o(i._printf_core)
    _printf_core                             0x08012195   Thumb Code   984  printf8.o(i._printf_core)
    i._printf_post_padding                   0x08012598   Section        0  printf8.o(i._printf_post_padding)
    _printf_post_padding                     0x08012599   Thumb Code    36  printf8.o(i._printf_post_padding)
    i._printf_pre_padding                    0x080125bc   Section        0  printf8.o(i._printf_pre_padding)
    _printf_pre_padding                      0x080125bd   Thumb Code    46  printf8.o(i._printf_pre_padding)
    i._sputc                                 0x080125ea   Section        0  printf8.o(i._sputc)
    _sputc                                   0x080125eb   Thumb Code    10  printf8.o(i._sputc)
    .constdata                               0x080125f4   Section     6080  lcd.o(.constdata)
    .constdata                               0x08013db4   Section      116  gd32f4xx_enet.o(.constdata)
    enet_reg_tab                             0x08013db4   Data         116  gd32f4xx_enet.o(.constdata)
    .constdata                               0x08013e28   Section       12  etharp.o(.constdata)
    .constdata                               0x08013e34   Section        4  ip_addr.o(.constdata)
    .constdata                               0x08013e38   Section        4  ip_addr.o(.constdata)
    .constdata                               0x08013e3c   Section       40  memp.o(.constdata)
    memp_sizes                               0x08013e3c   Data          20  memp.o(.constdata)
    memp_num                                 0x08013e50   Data          20  memp.o(.constdata)
    .constdata                               0x08013e64   Section       84  tcp.o(.constdata)
    .constdata                               0x08013eb8   Section       36  malloc.o(.constdata)
    .conststring                             0x08013edc   Section      136  lan8720.o(.conststring)
    .conststring                             0x08013f64   Section       68  etharp.o(.conststring)
    .conststring                             0x08013fa8   Section      140  dhcp.o(.conststring)
    .conststring                             0x08014034   Section      117  tcp.o(.conststring)
    .conststring                             0x080140ac   Section       85  tcp_out.o(.conststring)
    .ARM.__AT_0x10000000                     0x10000000   Section    61440  malloc.o(.ARM.__AT_0x10000000)
    .ARM.__AT_0x1000F000                     0x1000f000   Section     3840  malloc.o(.ARM.__AT_0x1000F000)
    .data                                    0x20000000   Section        8  systick.o(.data)
    count_1us                                0x20000000   Data           4  systick.o(.data)
    count_1ms                                0x20000004   Data           4  systick.o(.data)
    .data                                    0x20000008   Section        8  lan8720.o(.data)
    rx_packet_count                          0x20000008   Data           4  lan8720.o(.data)
    rx_error_count                           0x2000000c   Data           4  lan8720.o(.data)
    .data                                    0x20000010   Section        4  lcd.o(.data)
    .data                                    0x20000014   Section        2  usart0.o(.data)
    .data                                    0x20000018   Section        4  usart0.o(.data)
    .data                                    0x2000001c   Section       12  implement.o(.data)
    last_link_status                         0x2000001c   Data           1  implement.o(.data)
    status_print_timer                       0x20000020   Data           4  implement.o(.data)
    link_check_timer                         0x20000024   Data           4  implement.o(.data)
    .data                                    0x20000028   Section        4  system_gd32f4xx.o(.data)
    .data                                    0x2000002c   Section       28  gd32f4xx_enet.o(.data)
    enet_unknow_err                          0x20000034   Data           4  gd32f4xx_enet.o(.data)
    .data                                    0x20000048   Section       24  lwip_comm.o(.data)
    mem_check_timer                          0x20000058   Data           4  lwip_comm.o(.data)
    .data                                    0x20000060   Section        1  etharp.o(.data)
    etharp_cached_entry                      0x20000060   Data           1  etharp.o(.data)
    .data                                    0x20000064   Section       20  ip.o(.data)
    ip_id                                    0x20000064   Data           2  ip.o(.data)
    .data                                    0x20000078   Section        8  ip_frag.o(.data)
    ip_reass_pbufcount                       0x20000078   Data           2  ip_frag.o(.data)
    reassdatagrams                           0x2000007c   Data           4  ip_frag.o(.data)
    .data                                    0x20000080   Section        4  dhcp.o(.data)
    xid                                      0x20000080   Data           4  dhcp.o(.data)
    .data                                    0x20000084   Section       16  mem.o(.data)
    ram                                      0x20000088   Data           4  mem.o(.data)
    ram_end                                  0x2000008c   Data           4  mem.o(.data)
    lfree                                    0x20000090   Data           4  mem.o(.data)
    .data                                    0x20000094   Section        4  memp.o(.data)
    .data                                    0x20000098   Section       12  netif.o(.data)
    netif_num                                0x20000098   Data           1  netif.o(.data)
    .data                                    0x200000a4   Section        4  raw.o(.data)
    raw_pcbs                                 0x200000a4   Data           4  raw.o(.data)
    .data                                    0x200000a8   Section       36  tcp.o(.data)
    tcp_timer                                0x200000a9   Data           1  tcp.o(.data)
    tcp_timer_ctr                            0x200000aa   Data           1  tcp.o(.data)
    tcp_port                                 0x200000ac   Data           2  tcp.o(.data)
    iss                                      0x200000b0   Data           4  tcp.o(.data)
    .data                                    0x200000cc   Section       28  tcp_in.o(.data)
    flags                                    0x200000cc   Data           1  tcp_in.o(.data)
    recv_flags                               0x200000cd   Data           1  tcp_in.o(.data)
    tcplen                                   0x200000ce   Data           2  tcp_in.o(.data)
    tcphdr                                   0x200000d0   Data           4  tcp_in.o(.data)
    iphdr                                    0x200000d4   Data           4  tcp_in.o(.data)
    seqno                                    0x200000d8   Data           4  tcp_in.o(.data)
    ackno                                    0x200000dc   Data           4  tcp_in.o(.data)
    recv_data                                0x200000e0   Data           4  tcp_in.o(.data)
    .data                                    0x200000e8   Section       12  timers.o(.data)
    next_timeout                             0x200000e8   Data           4  timers.o(.data)
    timeouts_last_time                       0x200000ec   Data           4  timers.o(.data)
    tcpip_tcp_timer_active                   0x200000f0   Data           4  timers.o(.data)
    .data                                    0x200000f4   Section        8  udp.o(.data)
    udp_port                                 0x200000f4   Data           2  udp.o(.data)
    .data                                    0x200000fc   Section       36  malloc.o(.data)
    .bss                                     0x20000120   Section       14  lcd.o(.bss)
    .bss                                     0x20000130   Section    15300  gd32f4xx_enet.o(.bss)
    enet_initpara                            0x20000130   Data          60  gd32f4xx_enet.o(.bss)
    .bss                                     0x20003cf4   Section       76  lwip_comm.o(.bss)
    .bss                                     0x20003d40   Section      200  etharp.o(.bss)
    arp_table                                0x20003d40   Data         200  etharp.o(.bss)
    .bss                                     0x20003e08   Section       16  ip_addr.o(.bss)
    str                                      0x20003e08   Data          16  ip_addr.o(.bss)
    .bss                                     0x20003e18   Section       50  dhcp.o(.bss)
    .bss                                     0x20003e4c   Section       40  memp.o(.bss)
    memp_tab                                 0x20003e4c   Data          40  memp.o(.bss)
    .bss                                     0x20003e74   Section       16  tcp_in.o(.bss)
    inseg                                    0x20003e74   Data          16  tcp_in.o(.bss)
    .bss                                     0x20003e84   Section    102400  malloc.o(.bss)
    .bss                                     0x2001ce84   Section     6400  malloc.o(.bss)
    STACK                                    0x2001e788   Section     1024  startup_gd32f450_470.o(STACK)
    .ARM.__AT_0x68000000                     0x68000000   Section    204800  malloc.o(.ARM.__AT_0x68000000)
    .ARM.__AT_0x68032000                     0x68032000   Section    12800  malloc.o(.ARM.__AT_0x68032000)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080001bd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001c1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001c1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    NMI_Handler                              0x080001c5   Thumb Code     2  gd32f4xx_it.o(.text)
    HardFault_Handler                        0x080001c7   Thumb Code     2  gd32f4xx_it.o(.text)
    MemManage_Handler                        0x080001c9   Thumb Code     2  gd32f4xx_it.o(.text)
    BusFault_Handler                         0x080001cb   Thumb Code     2  gd32f4xx_it.o(.text)
    UsageFault_Handler                       0x080001cd   Thumb Code     2  gd32f4xx_it.o(.text)
    SVC_Handler                              0x080001cf   Thumb Code     2  gd32f4xx_it.o(.text)
    DebugMon_Handler                         0x080001d1   Thumb Code     2  gd32f4xx_it.o(.text)
    PendSV_Handler                           0x080001d3   Thumb Code     2  gd32f4xx_it.o(.text)
    SysTick_Handler                          0x080001d5   Thumb Code     2  gd32f4xx_it.o(.text)
    main                                     0x080001d7   Thumb Code    14  main.o(.text)
    systick_config                           0x080001e5   Thumb Code    64  systick.o(.text)
    delay_1us                                0x08000225   Thumb Code    62  systick.o(.text)
    delay_1ms                                0x08000263   Thumb Code    62  systick.o(.text)
    LED_Init                                 0x080002b1   Thumb Code    56  led.o(.text)
    KEY_Init                                 0x080002ed   Thumb Code    74  key.o(.text)
    Key_Scan                                 0x08000337   Thumb Code    32  key.o(.text)
    LAN8720_Get_Speed_Duplex                 0x08000361   Thumb Code    98  lan8720.o(.text)
    LAN8720_Get_Link_Status                  0x080003c3   Thumb Code    30  lan8720.o(.text)
    ETH_MACDMA_Config                        0x080003e1   Thumb Code  1368  lan8720.o(.text)
    LAN8720_Init                             0x08000939   Thumb Code   602  lan8720.o(.text)
    ENET_IRQHandler                          0x08000b93   Thumb Code    88  lan8720.o(.text)
    ETH_Rx_Packet                            0x08000beb   Thumb Code    72  lan8720.o(.text)
    ETH_Tx_Packet                            0x08000c33   Thumb Code    50  lan8720.o(.text)
    ETH_GetCurrentTxBuffer                   0x08000c65   Thumb Code     8  lan8720.o(.text)
    ETH_Mem_Free                             0x08000c6d   Thumb Code    26  lan8720.o(.text)
    ETH_Mem_Malloc                           0x08000c87   Thumb Code    44  lan8720.o(.text)
    LCD_WR_REG                               0x08000fad   Thumb Code     6  lcd.o(.text)
    LCD_WR_DATA                              0x08000fb3   Thumb Code     8  lcd.o(.text)
    LCD_RD_DATA                              0x08000fbb   Thumb Code    18  lcd.o(.text)
    LCD_WriteReg                             0x08000fcd   Thumb Code    10  lcd.o(.text)
    LCD_ReadReg                              0x08000fd7   Thumb Code    18  lcd.o(.text)
    LCD_WriteRAM_Prepare                     0x08000fe9   Thumb Code    10  lcd.o(.text)
    LCD_WriteRAM                             0x08000ff3   Thumb Code     8  lcd.o(.text)
    LCD_BGR2RGB                              0x08000ffb   Thumb Code    18  lcd.o(.text)
    opt_delay                                0x0800100d   Thumb Code     8  lcd.o(.text)
    LCD_SetCursor                            0x08001015   Thumb Code   318  lcd.o(.text)
    LCD_ReadPoint                            0x08001153   Thumb Code   260  lcd.o(.text)
    LCD_DisplayOn                            0x08001257   Thumb Code    66  lcd.o(.text)
    LCD_DisplayOff                           0x08001299   Thumb Code    64  lcd.o(.text)
    LCD_Scan_Dir                             0x080012d9   Thumb Code   422  lcd.o(.text)
    LCD_DrawPoint                            0x0800147f   Thumb Code    22  lcd.o(.text)
    LCD_Fast_DrawPoint                       0x08001495   Thumb Code   276  lcd.o(.text)
    LCD_SSD_BackLightSet                     0x080015a9   Thumb Code    52  lcd.o(.text)
    LCD_Display_Dir                          0x080015dd   Thumb Code   302  lcd.o(.text)
    LCD_Set_Window                           0x0800170b   Thumb Code   392  lcd.o(.text)
    LCD_Clear                                0x08001893   Thumb Code   102  lcd.o(.text)
    LCD_Init                                 0x080018f9   Thumb Code 11866  lcd.o(.text)
    LCD_Fill                                 0x08004753   Thumb Code   132  lcd.o(.text)
    LCD_Color_Fill                           0x080047d7   Thumb Code    84  lcd.o(.text)
    LCD_DrawLine                             0x0800482b   Thumb Code   150  lcd.o(.text)
    LCD_DrawRectangle                        0x080048c1   Thumb Code    54  lcd.o(.text)
    LCD_Draw_Circle                          0x080048f7   Thumb Code   176  lcd.o(.text)
    LCD_ShowChar                             0x080049a7   Thumb Code   184  lcd.o(.text)
    LCD_Pow                                  0x08004a5f   Thumb Code    16  lcd.o(.text)
    LCD_ShowNum                              0x08004a6f   Thumb Code   116  lcd.o(.text)
    LCD_ShowxNum                             0x08004ae3   Thumb Code   160  lcd.o(.text)
    LCD_ShowString                           0x08004b83   Thumb Code    82  lcd.o(.text)
    _sys_exit                                0x08004bd5   Thumb Code     2  usart0.o(.text)
    fputc                                    0x08004bd7   Thumb Code    30  usart0.o(.text)
    USART0_Config                            0x08004bf5   Thumb Code   168  usart0.o(.text)
    USART0_SendData                          0x08004c9d   Thumb Code    60  usart0.o(.text)
    USART0_IRQHandler                        0x08004cd9   Thumb Code    40  usart0.o(.text)
    show_address                             0x08004d11   Thumb Code   282  implement.o(.text)
    System_Init                              0x08004e2b   Thumb Code  1408  implement.o(.text)
    Implement                                0x080053ab   Thumb Code   208  implement.o(.text)
    Timer3_Init                              0x080057cd   Thumb Code   102  timer.o(.text)
    TIMER3_IRQHandler                        0x08005833   Thumb Code    36  timer.o(.text)
    SystemInit                               0x080058fd   Thumb Code   120  system_gd32f4xx.o(.text)
    SystemCoreClockUpdate                    0x08005975   Thumb Code   114  system_gd32f4xx.o(.text)
    enet_initpara_reset                      0x08005a1d   Thumb Code    36  gd32f4xx_enet.o(.text)
    enet_deinit                              0x08005a41   Thumb Code    24  gd32f4xx_enet.o(.text)
    enet_initpara_config                     0x08005a59   Thumb Code   218  gd32f4xx_enet.o(.text)
    enet_phy_write_read                      0x08005b33   Thumb Code    90  gd32f4xx_enet.o(.text)
    enet_phy_config                          0x08005b8d   Thumb Code   154  gd32f4xx_enet.o(.text)
    enet_init                                0x08005c27   Thumb Code   748  gd32f4xx_enet.o(.text)
    enet_software_reset                      0x08005f13   Thumb Code    44  gd32f4xx_enet.o(.text)
    enet_rxframe_drop                        0x08005f3f   Thumb Code    82  gd32f4xx_enet.o(.text)
    enet_rxframe_size_get                    0x08005f91   Thumb Code    68  gd32f4xx_enet.o(.text)
    enet_descriptors_chain_init              0x08005fd5   Thumb Code   118  gd32f4xx_enet.o(.text)
    enet_descriptors_ring_init               0x0800604b   Thumb Code   150  gd32f4xx_enet.o(.text)
    enet_frame_receive                       0x080060e1   Thumb Code   130  gd32f4xx_enet.o(.text)
    enet_frame_transmit                      0x08006163   Thumb Code   114  gd32f4xx_enet.o(.text)
    enet_transmit_checksum_config            0x080061d5   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_rx_enable                           0x080061e1   Thumb Code    22  gd32f4xx_enet.o(.text)
    enet_txfifo_flush                        0x080061f7   Thumb Code    40  gd32f4xx_enet.o(.text)
    enet_tx_enable                           0x0800621f   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_enable                              0x0800623b   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_rx_disable                          0x08006247   Thumb Code    46  gd32f4xx_enet.o(.text)
    enet_tx_disable                          0x08006275   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_disable                             0x08006291   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_mac_address_set                     0x0800629d   Thumb Code    32  gd32f4xx_enet.o(.text)
    enet_mac_address_get                     0x080062bd   Thumb Code    38  gd32f4xx_enet.o(.text)
    enet_flag_get                            0x080062e3   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_flag_clear                          0x080062ff   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_interrupt_enable                    0x08006311   Thumb Code    38  gd32f4xx_enet.o(.text)
    enet_interrupt_disable                   0x08006337   Thumb Code    38  gd32f4xx_enet.o(.text)
    enet_interrupt_flag_get                  0x0800635d   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_interrupt_flag_clear                0x08006379   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_registers_get                       0x0800638b   Thumb Code    34  gd32f4xx_enet.o(.text)
    enet_debug_status_get                    0x080063ad   Thumb Code    86  gd32f4xx_enet.o(.text)
    enet_address_filter_enable               0x08006403   Thumb Code    14  gd32f4xx_enet.o(.text)
    enet_address_filter_disable              0x08006411   Thumb Code    14  gd32f4xx_enet.o(.text)
    enet_address_filter_config               0x0800641f   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_phyloopback_enable                  0x08006431   Thumb Code    44  gd32f4xx_enet.o(.text)
    enet_phyloopback_disable                 0x0800645d   Thumb Code    44  gd32f4xx_enet.o(.text)
    enet_forward_feature_enable              0x08006489   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_forward_feature_disable             0x080064a5   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_fliter_feature_enable               0x080064c1   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_fliter_feature_disable              0x080064cb   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_pauseframe_generate                 0x080064d5   Thumb Code    24  gd32f4xx_enet.o(.text)
    enet_pauseframe_detect_config            0x080064ed   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_pauseframe_config                   0x080064ff   Thumb Code    26  gd32f4xx_enet.o(.text)
    enet_flowcontrol_threshold_config        0x08006519   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_flowcontrol_feature_enable          0x08006525   Thumb Code    26  gd32f4xx_enet.o(.text)
    enet_flowcontrol_feature_disable         0x0800653f   Thumb Code    26  gd32f4xx_enet.o(.text)
    enet_dmaprocess_state_get                0x08006559   Thumb Code     8  gd32f4xx_enet.o(.text)
    enet_dmaprocess_resume                   0x08006561   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_rxprocess_check_recovery            0x08006573   Thumb Code    32  gd32f4xx_enet.o(.text)
    enet_current_desc_address_get            0x08006593   Thumb Code     6  gd32f4xx_enet.o(.text)
    enet_desc_information_get                0x08006599   Thumb Code    80  gd32f4xx_enet.o(.text)
    enet_missed_frame_counter_get            0x080065e9   Thumb Code    16  gd32f4xx_enet.o(.text)
    enet_desc_flag_get                       0x080065f9   Thumb Code    14  gd32f4xx_enet.o(.text)
    enet_desc_flag_set                       0x08006607   Thumb Code     8  gd32f4xx_enet.o(.text)
    enet_desc_flag_clear                     0x0800660f   Thumb Code     8  gd32f4xx_enet.o(.text)
    enet_rx_desc_immediate_receive_complete_interrupt 0x08006617   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_rx_desc_delay_receive_complete_interrupt 0x08006621   Thumb Code    16  gd32f4xx_enet.o(.text)
    enet_dma_feature_enable                  0x08006631   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_dma_feature_disable                 0x0800663b   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_rx_desc_enhanced_status_get         0x08006645   Thumb Code    52  gd32f4xx_enet.o(.text)
    enet_desc_select_enhanced_mode           0x08006679   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_ptp_enhanced_descriptors_chain_init 0x08006685   Thumb Code   108  gd32f4xx_enet.o(.text)
    enet_ptp_enhanced_descriptors_ring_init  0x080066f1   Thumb Code   134  gd32f4xx_enet.o(.text)
    enet_ptpframe_receive_enhanced_mode      0x08006777   Thumb Code   178  gd32f4xx_enet.o(.text)
    enet_ptpframe_transmit_enhanced_mode     0x08006829   Thumb Code   156  gd32f4xx_enet.o(.text)
    enet_wum_filter_register_pointer_reset   0x080068c5   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_wum_filter_config                   0x080068d1   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_wum_feature_enable                  0x080068e3   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_wum_feature_disable                 0x080068ed   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_msc_counters_reset                  0x080068f7   Thumb Code    16  gd32f4xx_enet.o(.text)
    enet_msc_feature_enable                  0x08006907   Thumb Code    14  gd32f4xx_enet.o(.text)
    enet_msc_feature_disable                 0x08006915   Thumb Code    14  gd32f4xx_enet.o(.text)
    enet_msc_counters_preset_config          0x08006923   Thumb Code    26  gd32f4xx_enet.o(.text)
    enet_msc_counters_get                    0x0800693d   Thumb Code     6  gd32f4xx_enet.o(.text)
    enet_ptp_feature_enable                  0x08006943   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_ptp_feature_disable                 0x0800694d   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_ptp_timestamp_function_config       0x08006957   Thumb Code   172  gd32f4xx_enet.o(.text)
    enet_ptp_subsecond_increment_config      0x08006a03   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_ptp_timestamp_addend_config         0x08006a0d   Thumb Code     8  gd32f4xx_enet.o(.text)
    enet_ptp_timestamp_update_config         0x08006a15   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_ptp_expected_time_config            0x08006a27   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_ptp_system_time_get                 0x08006a33   Thumb Code    24  gd32f4xx_enet.o(.text)
    enet_ptp_pps_output_frequency_config     0x08006a4b   Thumb Code     8  gd32f4xx_enet.o(.text)
    exmc_norsram_deinit                      0x08006a81   Thumb Code    24  gd32f4xx_exmc.o(.text)
    exmc_norsram_struct_para_init            0x08006a99   Thumb Code    90  gd32f4xx_exmc.o(.text)
    exmc_norsram_init                        0x08006af3   Thumb Code   184  gd32f4xx_exmc.o(.text)
    exmc_norsram_enable                      0x08006bab   Thumb Code    16  gd32f4xx_exmc.o(.text)
    exmc_norsram_disable                     0x08006bbb   Thumb Code    16  gd32f4xx_exmc.o(.text)
    exmc_nand_deinit                         0x08006bcb   Thumb Code    24  gd32f4xx_exmc.o(.text)
    exmc_nand_struct_para_init               0x08006be3   Thumb Code    54  gd32f4xx_exmc.o(.text)
    exmc_nand_init                           0x08006c19   Thumb Code   134  gd32f4xx_exmc.o(.text)
    exmc_nand_enable                         0x08006c9f   Thumb Code    16  gd32f4xx_exmc.o(.text)
    exmc_nand_disable                        0x08006caf   Thumb Code    16  gd32f4xx_exmc.o(.text)
    exmc_pccard_deinit                       0x08006cbf   Thumb Code    26  gd32f4xx_exmc.o(.text)
    exmc_pccard_struct_para_init             0x08006cd9   Thumb Code    60  gd32f4xx_exmc.o(.text)
    exmc_pccard_init                         0x08006d15   Thumb Code   136  gd32f4xx_exmc.o(.text)
    exmc_pccard_enable                       0x08006d9d   Thumb Code    16  gd32f4xx_exmc.o(.text)
    exmc_pccard_disable                      0x08006dad   Thumb Code    16  gd32f4xx_exmc.o(.text)
    exmc_sdram_deinit                        0x08006dbd   Thumb Code    42  gd32f4xx_exmc.o(.text)
    exmc_sdram_struct_para_init              0x08006de7   Thumb Code    62  gd32f4xx_exmc.o(.text)
    exmc_sdram_init                          0x08006e25   Thumb Code   252  gd32f4xx_exmc.o(.text)
    exmc_sqpipsram_deinit                    0x08006f21   Thumb Code    30  gd32f4xx_exmc.o(.text)
    exmc_sqpipsram_struct_para_init          0x08006f3f   Thumb Code    20  gd32f4xx_exmc.o(.text)
    exmc_sqpipsram_init                      0x08006f53   Thumb Code    24  gd32f4xx_exmc.o(.text)
    exmc_norsram_consecutive_clock_config    0x08006f6b   Thumb Code    26  gd32f4xx_exmc.o(.text)
    exmc_norsram_page_size_config            0x08006f85   Thumb Code    22  gd32f4xx_exmc.o(.text)
    exmc_nand_ecc_config                     0x08006f9b   Thumb Code    26  gd32f4xx_exmc.o(.text)
    exmc_ecc_get                             0x08006fb5   Thumb Code    10  gd32f4xx_exmc.o(.text)
    exmc_sdram_readsample_enable             0x08006fbf   Thumb Code    28  gd32f4xx_exmc.o(.text)
    exmc_sdram_readsample_config             0x08006fdb   Thumb Code    22  gd32f4xx_exmc.o(.text)
    exmc_sdram_command_config                0x08006ff1   Thumb Code    24  gd32f4xx_exmc.o(.text)
    exmc_sdram_refresh_count_set             0x08007009   Thumb Code    28  gd32f4xx_exmc.o(.text)
    exmc_sdram_autorefresh_number_set        0x08007025   Thumb Code    28  gd32f4xx_exmc.o(.text)
    exmc_sdram_write_protection_config       0x08007041   Thumb Code    30  gd32f4xx_exmc.o(.text)
    exmc_sdram_bankstatus_get                0x0800705f   Thumb Code    24  gd32f4xx_exmc.o(.text)
    exmc_sqpipsram_read_command_set          0x08007077   Thumb Code    24  gd32f4xx_exmc.o(.text)
    exmc_sqpipsram_write_command_set         0x0800708f   Thumb Code    24  gd32f4xx_exmc.o(.text)
    exmc_sqpipsram_read_id_command_send      0x080070a7   Thumb Code    18  gd32f4xx_exmc.o(.text)
    exmc_sqpipsram_write_cmd_send            0x080070b9   Thumb Code    18  gd32f4xx_exmc.o(.text)
    exmc_sqpipsram_low_id_get                0x080070cb   Thumb Code    10  gd32f4xx_exmc.o(.text)
    exmc_sqpipsram_high_id_get               0x080070d5   Thumb Code    10  gd32f4xx_exmc.o(.text)
    exmc_sqpipsram_send_command_state_get    0x080070df   Thumb Code    28  gd32f4xx_exmc.o(.text)
    exmc_interrupt_enable                    0x080070fb   Thumb Code    44  gd32f4xx_exmc.o(.text)
    exmc_interrupt_disable                   0x08007127   Thumb Code    44  gd32f4xx_exmc.o(.text)
    exmc_flag_get                            0x08007153   Thumb Code    42  gd32f4xx_exmc.o(.text)
    exmc_flag_clear                          0x0800717d   Thumb Code    42  gd32f4xx_exmc.o(.text)
    exmc_interrupt_flag_get                  0x080071a7   Thumb Code    56  gd32f4xx_exmc.o(.text)
    exmc_interrupt_flag_clear                0x080071df   Thumb Code    46  gd32f4xx_exmc.o(.text)
    gpio_deinit                              0x08007211   Thumb Code   104  gd32f4xx_gpio.o(.text)
    gpio_mode_set                            0x08007279   Thumb Code    72  gd32f4xx_gpio.o(.text)
    gpio_output_options_set                  0x080072c1   Thumb Code    62  gd32f4xx_gpio.o(.text)
    gpio_bit_set                             0x080072ff   Thumb Code     4  gd32f4xx_gpio.o(.text)
    gpio_bit_reset                           0x08007303   Thumb Code     4  gd32f4xx_gpio.o(.text)
    gpio_bit_write                           0x08007307   Thumb Code    10  gd32f4xx_gpio.o(.text)
    gpio_port_write                          0x08007311   Thumb Code     4  gd32f4xx_gpio.o(.text)
    gpio_input_bit_get                       0x08007315   Thumb Code    10  gd32f4xx_gpio.o(.text)
    gpio_input_port_get                      0x0800731f   Thumb Code     6  gd32f4xx_gpio.o(.text)
    gpio_output_bit_get                      0x08007325   Thumb Code    10  gd32f4xx_gpio.o(.text)
    gpio_output_port_get                     0x0800732f   Thumb Code     6  gd32f4xx_gpio.o(.text)
    gpio_af_set                              0x08007335   Thumb Code    86  gd32f4xx_gpio.o(.text)
    gpio_pin_lock                            0x0800738b   Thumb Code    16  gd32f4xx_gpio.o(.text)
    gpio_bit_toggle                          0x0800739b   Thumb Code     4  gd32f4xx_gpio.o(.text)
    gpio_port_toggle                         0x0800739f   Thumb Code     8  gd32f4xx_gpio.o(.text)
    nvic_priority_group_set                  0x080073a9   Thumb Code    10  gd32f4xx_misc.o(.text)
    nvic_irq_enable                          0x080073b3   Thumb Code   144  gd32f4xx_misc.o(.text)
    nvic_irq_disable                         0x08007443   Thumb Code    22  gd32f4xx_misc.o(.text)
    nvic_vector_table_set                    0x08007459   Thumb Code    18  gd32f4xx_misc.o(.text)
    system_lowpower_set                      0x0800746b   Thumb Code    12  gd32f4xx_misc.o(.text)
    system_lowpower_reset                    0x08007477   Thumb Code    12  gd32f4xx_misc.o(.text)
    systick_clksource_set                    0x08007483   Thumb Code    24  gd32f4xx_misc.o(.text)
    rcu_flag_get                             0x080074a9   Thumb Code    30  gd32f4xx_rcu.o(.text)
    rcu_osci_stab_wait                       0x080074c7   Thumb Code   232  gd32f4xx_rcu.o(.text)
    rcu_deinit                               0x080075af   Thumb Code    96  gd32f4xx_rcu.o(.text)
    rcu_periph_clock_enable                  0x0800760f   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_periph_clock_disable                 0x08007629   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_periph_clock_sleep_enable            0x08007643   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_periph_clock_sleep_disable           0x0800765d   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_periph_reset_enable                  0x08007677   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_periph_reset_disable                 0x08007691   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_bkp_reset_enable                     0x080076ab   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_bkp_reset_disable                    0x080076b9   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_system_clock_source_config           0x080076c7   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_system_clock_source_get              0x080076d7   Thumb Code    12  gd32f4xx_rcu.o(.text)
    rcu_ahb_clock_config                     0x080076e3   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_apb1_clock_config                    0x080076f3   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_apb2_clock_config                    0x08007703   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_ckout0_config                        0x08007713   Thumb Code    18  gd32f4xx_rcu.o(.text)
    rcu_ckout1_config                        0x08007725   Thumb Code    18  gd32f4xx_rcu.o(.text)
    rcu_pll_config                           0x08007737   Thumb Code   100  gd32f4xx_rcu.o(.text)
    rcu_plli2s_config                        0x0800779b   Thumb Code    36  gd32f4xx_rcu.o(.text)
    rcu_pllsai_config                        0x080077bf   Thumb Code    62  gd32f4xx_rcu.o(.text)
    rcu_rtc_clock_config                     0x080077fd   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_rtc_div_config                       0x0800780d   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_i2s_clock_config                     0x0800781d   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_ck48m_clock_config                   0x0800782d   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_pll48m_clock_config                  0x0800783d   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_timer_clock_prescaler_config         0x0800784d   Thumb Code    24  gd32f4xx_rcu.o(.text)
    rcu_tli_clock_div_config                 0x08007865   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_all_reset_flag_clear                 0x08007875   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_interrupt_flag_get                   0x08007883   Thumb Code    58  gd32f4xx_rcu.o(.text)
    rcu_interrupt_flag_clear                 0x080078bd   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_interrupt_enable                     0x080078d7   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_interrupt_disable                    0x080078f1   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_lxtal_drive_capability_config        0x0800790b   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_osci_on                              0x08007919   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_osci_off                             0x08007933   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_osci_bypass_mode_enable              0x0800794d   Thumb Code    60  gd32f4xx_rcu.o(.text)
    rcu_osci_bypass_mode_disable             0x08007989   Thumb Code    60  gd32f4xx_rcu.o(.text)
    rcu_hxtal_clock_monitor_enable           0x080079c5   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_hxtal_clock_monitor_disable          0x080079d3   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_irc16m_adjust_value_set              0x080079e1   Thumb Code    22  gd32f4xx_rcu.o(.text)
    rcu_voltage_key_unlock                   0x080079f7   Thumb Code    10  gd32f4xx_rcu.o(.text)
    rcu_deepsleep_voltage_set                0x08007a01   Thumb Code    12  gd32f4xx_rcu.o(.text)
    rcu_spread_spectrum_config               0x08007a0d   Thumb Code    24  gd32f4xx_rcu.o(.text)
    rcu_spread_spectrum_enable               0x08007a25   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_spread_spectrum_disable              0x08007a33   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_clock_freq_get                       0x08007a41   Thumb Code   194  gd32f4xx_rcu.o(.text)
    syscfg_deinit                            0x08007b29   Thumb Code    22  gd32f4xx_syscfg.o(.text)
    syscfg_bootmode_config                   0x08007b3f   Thumb Code    18  gd32f4xx_syscfg.o(.text)
    syscfg_fmc_swap_config                   0x08007b51   Thumb Code    14  gd32f4xx_syscfg.o(.text)
    syscfg_exmc_swap_config                  0x08007b5f   Thumb Code    14  gd32f4xx_syscfg.o(.text)
    syscfg_exti_line_config                  0x08007b6d   Thumb Code    64  gd32f4xx_syscfg.o(.text)
    syscfg_enet_phy_interface_config         0x08007bad   Thumb Code    16  gd32f4xx_syscfg.o(.text)
    syscfg_compensation_config               0x08007bbd   Thumb Code    16  gd32f4xx_syscfg.o(.text)
    syscfg_flag_get                          0x08007bcd   Thumb Code    18  gd32f4xx_syscfg.o(.text)
    timer_deinit                             0x08007be5   Thumb Code   190  gd32f4xx_timer.o(.text)
    timer_struct_para_init                   0x08007ca3   Thumb Code    20  gd32f4xx_timer.o(.text)
    timer_init                               0x08007cb7   Thumb Code   122  gd32f4xx_timer.o(.text)
    timer_enable                             0x08007d31   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_disable                            0x08007d3b   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_auto_reload_shadow_enable          0x08007d45   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_auto_reload_shadow_disable         0x08007d4f   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_update_event_enable                0x08007d59   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_update_event_disable               0x08007d63   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_counter_alignment                  0x08007d6d   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_counter_up_direction               0x08007d7d   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_counter_down_direction             0x08007d87   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_prescaler_config                   0x08007d91   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_repetition_value_config            0x08007da1   Thumb Code     4  gd32f4xx_timer.o(.text)
    timer_autoreload_value_config            0x08007da5   Thumb Code     4  gd32f4xx_timer.o(.text)
    timer_counter_value_config               0x08007da9   Thumb Code     4  gd32f4xx_timer.o(.text)
    timer_counter_read                       0x08007dad   Thumb Code     4  gd32f4xx_timer.o(.text)
    timer_prescaler_read                     0x08007db1   Thumb Code     6  gd32f4xx_timer.o(.text)
    timer_single_pulse_mode_config           0x08007db7   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_update_source_config               0x08007dcf   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_dma_enable                         0x08007de7   Thumb Code     8  gd32f4xx_timer.o(.text)
    timer_dma_disable                        0x08007def   Thumb Code     8  gd32f4xx_timer.o(.text)
    timer_channel_dma_request_source_select  0x08007df7   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_dma_transfer_config                0x08007e0f   Thumb Code    22  gd32f4xx_timer.o(.text)
    timer_event_software_generate            0x08007e25   Thumb Code     8  gd32f4xx_timer.o(.text)
    timer_break_struct_para_init             0x08007e2d   Thumb Code    18  gd32f4xx_timer.o(.text)
    timer_break_config                       0x08007e3f   Thumb Code    32  gd32f4xx_timer.o(.text)
    timer_break_enable                       0x08007e5f   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_break_disable                      0x08007e69   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_automatic_output_enable            0x08007e73   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_automatic_output_disable           0x08007e7d   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_primary_output_config              0x08007e87   Thumb Code    20  gd32f4xx_timer.o(.text)
    timer_channel_control_shadow_config      0x08007e9b   Thumb Code    20  gd32f4xx_timer.o(.text)
    timer_channel_control_shadow_update_config 0x08007eaf   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_channel_output_struct_para_init    0x08007ec7   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_channel_output_config              0x08007ed7   Thumb Code   490  gd32f4xx_timer.o(.text)
    timer_channel_output_mode_config         0x080080c1   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_output_pulse_value_config  0x0800810f   Thumb Code    30  gd32f4xx_timer.o(.text)
    timer_channel_output_shadow_config       0x0800812d   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_output_fast_config         0x0800817b   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_output_clear_config        0x080081c9   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_output_polarity_config     0x08008217   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_complementary_output_polarity_config 0x08008265   Thumb Code    58  gd32f4xx_timer.o(.text)
    timer_channel_output_state_config        0x0800829f   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_complementary_output_state_config 0x080082ed   Thumb Code    58  gd32f4xx_timer.o(.text)
    timer_channel_input_struct_para_init     0x08008327   Thumb Code    14  gd32f4xx_timer.o(.text)
    timer_channel_input_capture_prescaler_config 0x08008335   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_input_capture_config               0x08008383   Thumb Code   298  gd32f4xx_timer.o(.text)
    timer_channel_capture_value_register_read 0x080084ad   Thumb Code    34  gd32f4xx_timer.o(.text)
    timer_input_pwm_capture_config           0x080084cf   Thumb Code   332  gd32f4xx_timer.o(.text)
    timer_hall_mode_config                   0x0800861b   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_input_trigger_source_select        0x08008633   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_master_output_trigger_source_select 0x08008643   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_slave_mode_select                  0x08008653   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_master_slave_mode_config           0x08008663   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_external_trigger_config            0x0800867b   Thumb Code    28  gd32f4xx_timer.o(.text)
    timer_quadrature_decoder_mode_config     0x08008697   Thumb Code    64  gd32f4xx_timer.o(.text)
    timer_internal_clock_config              0x080086d7   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_internal_trigger_as_external_clock_config 0x080086e1   Thumb Code    26  gd32f4xx_timer.o(.text)
    timer_external_trigger_as_external_clock_config 0x080086fb   Thumb Code   156  gd32f4xx_timer.o(.text)
    timer_external_clock_mode0_config        0x08008797   Thumb Code    26  gd32f4xx_timer.o(.text)
    timer_external_clock_mode1_config        0x080087b1   Thumb Code    18  gd32f4xx_timer.o(.text)
    timer_external_clock_mode1_disable       0x080087c3   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_channel_remap_config               0x080087cd   Thumb Code     4  gd32f4xx_timer.o(.text)
    timer_write_chxval_register_config       0x080087d1   Thumb Code    30  gd32f4xx_timer.o(.text)
    timer_output_value_selection_config      0x080087ef   Thumb Code    30  gd32f4xx_timer.o(.text)
    timer_flag_get                           0x0800880d   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_flag_clear                         0x08008817   Thumb Code     6  gd32f4xx_timer.o(.text)
    timer_interrupt_enable                   0x0800881d   Thumb Code     8  gd32f4xx_timer.o(.text)
    timer_interrupt_disable                  0x08008825   Thumb Code     8  gd32f4xx_timer.o(.text)
    timer_interrupt_flag_get                 0x0800882d   Thumb Code    20  gd32f4xx_timer.o(.text)
    timer_interrupt_flag_clear               0x08008841   Thumb Code     6  gd32f4xx_timer.o(.text)
    usart_deinit                             0x08008849   Thumb Code   116  gd32f4xx_usart.o(.text)
    usart_baudrate_set                       0x080088bd   Thumb Code   118  gd32f4xx_usart.o(.text)
    usart_parity_config                      0x08008933   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_word_length_set                    0x08008943   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_stop_bit_set                       0x08008953   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_enable                             0x08008963   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_disable                            0x0800896d   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_transmit_config                    0x08008977   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_receive_config                     0x08008983   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_data_first_config                  0x0800898f   Thumb Code    14  gd32f4xx_usart.o(.text)
    usart_invert_config                      0x0800899d   Thumb Code    76  gd32f4xx_usart.o(.text)
    usart_oversample_config                  0x080089e9   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_sample_bit_config                  0x080089f9   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_receiver_timeout_enable            0x08008a09   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_receiver_timeout_disable           0x08008a15   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_receiver_timeout_threshold_config  0x08008a21   Thumb Code    18  gd32f4xx_usart.o(.text)
    usart_data_transmit                      0x08008a33   Thumb Code     8  gd32f4xx_usart.o(.text)
    usart_data_receive                       0x08008a3b   Thumb Code     8  gd32f4xx_usart.o(.text)
    usart_address_config                     0x08008a43   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_mute_mode_enable                   0x08008a57   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_mute_mode_disable                  0x08008a61   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_mute_mode_wakeup_config            0x08008a6b   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_lin_mode_enable                    0x08008a7b   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_lin_mode_disable                   0x08008a85   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_lin_break_detection_length_config  0x08008a8f   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_send_break                         0x08008aa3   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_halfduplex_enable                  0x08008aad   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_halfduplex_disable                 0x08008ab7   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_synchronous_clock_enable           0x08008ac1   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_synchronous_clock_disable          0x08008acb   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_synchronous_clock_config           0x08008ad5   Thumb Code    30  gd32f4xx_usart.o(.text)
    usart_guard_time_config                  0x08008af3   Thumb Code    24  gd32f4xx_usart.o(.text)
    usart_smartcard_mode_enable              0x08008b0b   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_smartcard_mode_disable             0x08008b15   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_smartcard_mode_nack_enable         0x08008b1f   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_smartcard_mode_nack_disable        0x08008b29   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_smartcard_autoretry_config         0x08008b33   Thumb Code    24  gd32f4xx_usart.o(.text)
    usart_block_length_config                0x08008b4b   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_irda_mode_enable                   0x08008b5f   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_irda_mode_disable                  0x08008b69   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_prescaler_config                   0x08008b73   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_irda_lowpower_config               0x08008b83   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_hardware_flow_rts_config           0x08008b97   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_hardware_flow_cts_config           0x08008ba3   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_break_frame_coherence_config       0x08008baf   Thumb Code    22  gd32f4xx_usart.o(.text)
    usart_parity_check_coherence_config      0x08008bc5   Thumb Code    22  gd32f4xx_usart.o(.text)
    usart_hardware_flow_coherence_config     0x08008bdb   Thumb Code    22  gd32f4xx_usart.o(.text)
    usart_dma_receive_config                 0x08008bf1   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_dma_transmit_config                0x08008bfd   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_flag_get                           0x08008c09   Thumb Code    22  gd32f4xx_usart.o(.text)
    usart_flag_clear                         0x08008c1f   Thumb Code    30  gd32f4xx_usart.o(.text)
    usart_interrupt_enable                   0x08008c3d   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_interrupt_disable                  0x08008c51   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_interrupt_flag_get                 0x08008c65   Thumb Code    48  gd32f4xx_usart.o(.text)
    usart_interrupt_flag_clear               0x08008c95   Thumb Code    20  gd32f4xx_usart.o(.text)
    Reset_Handler                            0x08008ca9   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel0_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel1_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel2_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI10_15_IRQHandler                     0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SDIO_IRQHandler                          0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER2_IRQHandler                        0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER6_IRQHandler                        0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART2_IRQHandler                        0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x08008cc3   Thumb Code     0  startup_gd32f450_470.o(.text)
    lwip_comm_mem_free                       0x08008ccd   Thumb Code    26  lwip_comm.o(.text)
    lwip_comm_mem_malloc                     0x08008ce7   Thumb Code    50  lwip_comm.o(.text)
    lwip_comm_default_ip_set                 0x08008d19   Thumb Code    74  lwip_comm.o(.text)
    lwip_comm_init                           0x08008d63   Thumb Code   236  lwip_comm.o(.text)
    lwip_pkt_handle                          0x08008e4f   Thumb Code     8  lwip_comm.o(.text)
    lwip_dhcp_process_handle                 0x08008e57   Thumb Code  1354  lwip_comm.o(.text)
    lwip_periodic_handle                     0x080093a1   Thumb Code   158  lwip_comm.o(.text)
    lwip_get_network_stats                   0x0800943f   Thumb Code   178  lwip_comm.o(.text)
    etharp_tmr                               0x080097bd   Thumb Code    66  etharp.o(.text)
    etharp_cleanup_netif                     0x08009a1f   Thumb Code    42  etharp.o(.text)
    etharp_find_addr                         0x08009a49   Thumb Code    74  etharp.o(.text)
    etharp_request                           0x08009dcb   Thumb Code    32  etharp.o(.text)
    etharp_query                             0x08009e3f   Thumb Code   298  etharp.o(.text)
    etharp_output                            0x08009f69   Thumb Code   306  etharp.o(.text)
    ethernet_input                           0x0800a09b   Thumb Code   380  etharp.o(.text)
    ethernetif_input                         0x0800a271   Thumb Code   116  ethernetif.o(.text)
    ethernetif_init                          0x0800a2e5   Thumb Code    48  ethernetif.o(.text)
    icmp_input                               0x0800a40d   Thumb Code   310  icmp.o(.text)
    icmp_dest_unreach                        0x0800a5bb   Thumb Code     6  icmp.o(.text)
    icmp_time_exceeded                       0x0800a5c1   Thumb Code     6  icmp.o(.text)
    inet_chksum_pseudo                       0x0800a793   Thumb Code   136  inet_chksum.o(.text)
    inet_chksum_pseudo_partial               0x0800a81b   Thumb Code   178  inet_chksum.o(.text)
    inet_chksum                              0x0800a8cd   Thumb Code    12  inet_chksum.o(.text)
    inet_chksum_pbuf                         0x0800a8d9   Thumb Code    72  inet_chksum.o(.text)
    ip_route                                 0x0800a989   Thumb Code    54  ip.o(.text)
    ip_input                                 0x0800a9bf   Thumb Code   366  ip.o(.text)
    ip_output_if                             0x0800ab2d   Thumb Code   210  ip.o(.text)
    ip_output                                0x0800abff   Thumb Code    72  ip.o(.text)
    ip4_addr_isbroadcast                     0x0800ace5   Thumb Code    46  ip_addr.o(.text)
    ip4_addr_netmask_valid                   0x0800ad13   Thumb Code    38  ip_addr.o(.text)
    ipaddr_aton                              0x0800ad39   Thumb Code   262  ip_addr.o(.text)
    ipaddr_addr                              0x0800ae3f   Thumb Code    20  ip_addr.o(.text)
    ipaddr_ntoa_r                            0x0800ae53   Thumb Code   118  ip_addr.o(.text)
    ipaddr_ntoa                              0x0800aec9   Thumb Code     6  ip_addr.o(.text)
    ip_reass_tmr                             0x0800b037   Thumb Code    40  ip_frag.o(.text)
    ip_reass                                 0x0800b225   Thumb Code   824  ip_frag.o(.text)
    ip_frag                                  0x0800b5a9   Thumb Code   382  ip_frag.o(.text)
    lwip_htons                               0x0800b765   Thumb Code     4  def.o(.text)
    lwip_ntohs                               0x0800b769   Thumb Code     4  def.o(.text)
    lwip_htonl                               0x0800b76d   Thumb Code     4  def.o(.text)
    lwip_ntohl                               0x0800b771   Thumb Code     4  def.o(.text)
    dhcp_renew                               0x0800bee1   Thumb Code   132  dhcp.o(.text)
    dhcp_coarse_tmr                          0x0800bfe7   Thumb Code    90  dhcp.o(.text)
    dhcp_release                             0x0800c0dd   Thumb Code   160  dhcp.o(.text)
    dhcp_fine_tmr                            0x0800c2b1   Thumb Code    46  dhcp.o(.text)
    dhcp_set_struct                          0x0800c2df   Thumb Code    80  dhcp.o(.text)
    dhcp_cleanup                             0x0800c32f   Thumb Code    40  dhcp.o(.text)
    dhcp_stop                                0x0800c357   Thumb Code    92  dhcp.o(.text)
    dhcp_start                               0x0800c8bd   Thumb Code   220  dhcp.o(.text)
    dhcp_inform                              0x0800c999   Thumb Code   168  dhcp.o(.text)
    dhcp_network_changed                     0x0800ca41   Thumb Code    66  dhcp.o(.text)
    dhcp_arp_reply                           0x0800ca83   Thumb Code    48  dhcp.o(.text)
    lwip_init                                0x0800cc59   Thumb Code    30  init.o(.text)
    mem_init                                 0x0800cd13   Thumb Code    44  mem.o(.text)
    mem_free                                 0x0800cd3f   Thumb Code   108  mem.o(.text)
    mem_trim                                 0x0800cdab   Thumb Code   240  mem.o(.text)
    mem_malloc                               0x0800ce9b   Thumb Code   252  mem.o(.text)
    mem_calloc                               0x0800cf97   Thumb Code    26  mem.o(.text)
    memp_get_memorysize                      0x0800d1a9   Thumb Code     6  memp.o(.text)
    memp_init                                0x0800d1af   Thumb Code    80  memp.o(.text)
    memp_malloc                              0x0800d1ff   Thumb Code    62  memp.o(.text)
    memp_free                                0x0800d23d   Thumb Code    40  memp.o(.text)
    netif_init                               0x0800d325   Thumb Code     2  netif.o(.text)
    netif_set_gw                             0x0800d327   Thumb Code     8  netif.o(.text)
    netif_set_netmask                        0x0800d32f   Thumb Code     8  netif.o(.text)
    netif_set_ipaddr                         0x0800d337   Thumb Code    96  netif.o(.text)
    netif_set_addr                           0x0800d397   Thumb Code    30  netif.o(.text)
    netif_add                                0x0800d3b5   Thumb Code    96  netif.o(.text)
    netif_set_default                        0x0800d415   Thumb Code     6  netif.o(.text)
    netif_set_down                           0x0800d41b   Thumb Code    26  netif.o(.text)
    netif_remove                             0x0800d435   Thumb Code    68  netif.o(.text)
    netif_find                               0x0800d479   Thumb Code    58  netif.o(.text)
    netif_set_up                             0x0800d4b3   Thumb Code    32  netif.o(.text)
    netif_set_link_up                        0x0800d4d3   Thumb Code    56  netif.o(.text)
    netif_set_link_down                      0x0800d50b   Thumb Code    18  netif.o(.text)
    pbuf_free                                0x0800d591   Thumb Code   170  pbuf.o(.text)
    pbuf_alloc                               0x0800d63b   Thumb Code   460  pbuf.o(.text)
    pbuf_alloced_custom                      0x0800d807   Thumb Code   100  pbuf.o(.text)
    pbuf_realloc                             0x0800d86b   Thumb Code   186  pbuf.o(.text)
    pbuf_header                              0x0800d925   Thumb Code   806  pbuf.o(.text)
    pbuf_clen                                0x0800dc4b   Thumb Code    18  pbuf.o(.text)
    pbuf_ref                                 0x0800dc5d   Thumb Code    12  pbuf.o(.text)
    pbuf_cat                                 0x0800dc69   Thumb Code   108  pbuf.o(.text)
    pbuf_chain                               0x0800dcd5   Thumb Code    16  pbuf.o(.text)
    pbuf_dechain                             0x0800dce5   Thumb Code   102  pbuf.o(.text)
    pbuf_copy                                0x0800dd4b   Thumb Code   258  pbuf.o(.text)
    pbuf_copy_partial                        0x0800de4d   Thumb Code   130  pbuf.o(.text)
    pbuf_take                                0x0800decf   Thumb Code   146  pbuf.o(.text)
    pbuf_coalesce                            0x0800df61   Thumb Code    66  pbuf.o(.text)
    pbuf_get_at                              0x0800dfa3   Thumb Code    24  pbuf.o(.text)
    pbuf_memcmp                              0x0800dfbb   Thumb Code   646  pbuf.o(.text)
    pbuf_memfind                             0x0800e241   Thumb Code    56  pbuf.o(.text)
    pbuf_strstr                              0x0800e279   Thumb Code    50  pbuf.o(.text)
    raw_input                                0x0800e2ad   Thumb Code   104  raw.o(.text)
    raw_bind                                 0x0800e315   Thumb Code    10  raw.o(.text)
    raw_connect                              0x0800e31f   Thumb Code    10  raw.o(.text)
    raw_recv                                 0x0800e329   Thumb Code     6  raw.o(.text)
    raw_sendto                               0x0800e32f   Thumb Code   160  raw.o(.text)
    raw_send                                 0x0800e3cf   Thumb Code     4  raw.o(.text)
    raw_remove                               0x0800e3d3   Thumb Code    40  raw.o(.text)
    raw_new                                  0x0800e3fb   Thumb Code    38  raw.o(.text)
    tcp_init                                 0x0800e4a5   Thumb Code     2  tcp.o(.text)
    tcp_seg_free                             0x0800e4a7   Thumb Code    28  tcp.o(.text)
    tcp_segs_free                            0x0800e4c3   Thumb Code    18  tcp.o(.text)
    tcp_pcb_purge                            0x0800e4d5   Thumb Code    58  tcp.o(.text)
    tcp_slowtmr                              0x0800e50f   Thumb Code   754  tcp.o(.text)
    tcp_pcb_remove                           0x0800e801   Thumb Code   126  tcp.o(.text)
    tcp_close                                0x0800ebad   Thumb Code    18  tcp.o(.text)
    tcp_update_rcv_ann_wnd                   0x0800ebbf   Thumb Code    80  tcp.o(.text)
    tcp_recved                               0x0800ec0f   Thumb Code   112  tcp.o(.text)
    tcp_recv_null                            0x0800ec7f   Thumb Code    34  tcp.o(.text)
    tcp_process_refused_data                 0x0800eca1   Thumb Code   100  tcp.o(.text)
    tcp_fasttmr                              0x0800ed05   Thumb Code    88  tcp.o(.text)
    tcp_tmr                                  0x0800ed5d   Thumb Code    30  tcp.o(.text)
    tcp_shutdown                             0x0800ed7b   Thumb Code    76  tcp.o(.text)
    tcp_abandon                              0x0800edc7   Thumb Code   162  tcp.o(.text)
    tcp_abort                                0x0800ee69   Thumb Code     4  tcp.o(.text)
    tcp_bind                                 0x0800ef81   Thumb Code   136  tcp.o(.text)
    tcp_listen_with_backlog                  0x0800f00f   Thumb Code   146  tcp.o(.text)
    tcp_eff_send_mss                         0x0800f0a1   Thumb Code    32  tcp.o(.text)
    tcp_next_iss                             0x0800f0c1   Thumb Code    12  tcp.o(.text)
    tcp_connect                              0x0800f0cd   Thumb Code   266  tcp.o(.text)
    tcp_setprio                              0x0800f1d7   Thumb Code     4  tcp.o(.text)
    tcp_alloc                                0x0800f1db   Thumb Code   236  tcp.o(.text)
    tcp_new                                  0x0800f2c7   Thumb Code     4  tcp.o(.text)
    tcp_arg                                  0x0800f2cb   Thumb Code     4  tcp.o(.text)
    tcp_recv                                 0x0800f2cf   Thumb Code    34  tcp.o(.text)
    tcp_sent                                 0x0800f2f1   Thumb Code    34  tcp.o(.text)
    tcp_err                                  0x0800f313   Thumb Code    36  tcp.o(.text)
    tcp_accept                               0x0800f337   Thumb Code     4  tcp.o(.text)
    tcp_poll                                 0x0800f33b   Thumb Code    42  tcp.o(.text)
    tcp_debug_state_str                      0x0800f365   Thumb Code    10  tcp.o(.text)
    tcp_input                                0x08010065   Thumb Code  1212  tcp_in.o(.text)
    tcp_enqueue_flags                        0x080106cb   Thumb Code   286  tcp_out.o(.text)
    tcp_send_fin                             0x080107e9   Thumb Code    68  tcp_out.o(.text)
    tcp_write                                0x08010901   Thumb Code  1410  tcp_out.o(.text)
    tcp_send_empty_ack                       0x08010e83   Thumb Code    72  tcp_out.o(.text)
    tcp_output                               0x080110db   Thumb Code   440  tcp_out.o(.text)
    tcp_rst                                  0x08011293   Thumb Code   144  tcp_out.o(.text)
    tcp_rexmit_rto                           0x08011323   Thumb Code    44  tcp_out.o(.text)
    tcp_rexmit                               0x0801134f   Thumb Code    82  tcp_out.o(.text)
    tcp_rexmit_fast                          0x080113a1   Thumb Code    88  tcp_out.o(.text)
    tcp_keepalive                            0x080113f9   Thumb Code    60  tcp_out.o(.text)
    tcp_zero_window_probe                    0x08011435   Thumb Code   220  tcp_out.o(.text)
    sys_timeout                              0x08011511   Thumb Code   104  timers.o(.text)
    tcp_timer_needed                         0x0801159f   Thumb Code    34  timers.o(.text)
    sys_timeouts_init                        0x08011611   Thumb Code    60  timers.o(.text)
    sys_untimeout                            0x0801164d   Thumb Code    68  timers.o(.text)
    sys_check_timeouts                       0x08011691   Thumb Code    74  timers.o(.text)
    sys_restart_timeouts                     0x080116db   Thumb Code    12  timers.o(.text)
    udp_init                                 0x0801179d   Thumb Code     2  udp.o(.text)
    udp_input                                0x0801179f   Thumb Code   388  udp.o(.text)
    udp_bind                                 0x08011923   Thumb Code   192  udp.o(.text)
    udp_sendto_if                            0x080119e3   Thumb Code   202  udp.o(.text)
    udp_sendto                               0x08011aad   Thumb Code    40  udp.o(.text)
    udp_send                                 0x08011ad5   Thumb Code     6  udp.o(.text)
    udp_connect                              0x08011adb   Thumb Code    70  udp.o(.text)
    udp_disconnect                           0x08011b21   Thumb Code    16  udp.o(.text)
    udp_recv                                 0x08011b31   Thumb Code     6  udp.o(.text)
    udp_remove                               0x08011b37   Thumb Code    40  udp.o(.text)
    udp_new                                  0x08011b5f   Thumb Code    26  udp.o(.text)
    sys_now                                  0x08011c39   Thumb Code     6  sys_arch.o(.text)
    mymemcpy                                 0x08011c45   Thumb Code    16  malloc.o(.text)
    mymemset                                 0x08011c55   Thumb Code    12  malloc.o(.text)
    my_mem_init                              0x08011c61   Thumb Code    52  malloc.o(.text)
    my_mem_perused                           0x08011c95   Thumb Code    48  malloc.o(.text)
    my_mem_malloc                            0x08011cc5   Thumb Code   136  malloc.o(.text)
    my_mem_free                              0x08011d4d   Thumb Code    82  malloc.o(.text)
    myfree                                   0x08011d9f   Thumb Code    18  malloc.o(.text)
    mymalloc                                 0x08011db1   Thumb Code    28  malloc.o(.text)
    myrealloc                                0x08011dcd   Thumb Code    54  malloc.o(.text)
    __aeabi_memcpy                           0x08011e0d   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08011e0d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08011e0d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08011e31   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08011e31   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08011e31   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08011e3f   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08011e3f   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08011e3f   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08011e43   Thumb Code    18  memseta.o(.text)
    strlen                                   0x08011e55   Thumb Code    14  strlen.o(.text)
    memcmp                                   0x08011e63   Thumb Code    26  memcmp.o(.text)
    __aeabi_dmul                             0x08011e7d   Thumb Code   228  dmul.o(.text)
    __aeabi_ui2d                             0x08011f61   Thumb Code    26  dfltui.o(.text)
    __aeabi_d2uiz                            0x08011f7b   Thumb Code    50  dfixui.o(.text)
    __aeabi_uldivmod                         0x08011fad   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsr                             0x0801200f   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0801200f   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x0801202f   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x0801202f   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0801204d   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x080120e9   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080120e9   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x0801210d   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0801210d   Thumb Code     0  llshl.o(.text)
    __0printf$8                              0x0801212d   Thumb Code    22  printf8.o(i.__0printf$8)
    __1printf$8                              0x0801212d   Thumb Code     0  printf8.o(i.__0printf$8)
    __2printf                                0x0801212d   Thumb Code     0  printf8.o(i.__0printf$8)
    __0sprintf$8                             0x0801214d   Thumb Code    34  printf8.o(i.__0sprintf$8)
    __1sprintf$8                             0x0801214d   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __2sprintf                               0x0801214d   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __scatterload_copy                       0x08012175   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08012183   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08012185   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    asc2_1206                                0x080125f4   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x08012a68   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x08013058   Data        3420  lcd.o(.constdata)
    ethbroadcast                             0x08013e28   Data           6  etharp.o(.constdata)
    ethzero                                  0x08013e2e   Data           6  etharp.o(.constdata)
    ip_addr_any                              0x08013e34   Data           4  ip_addr.o(.constdata)
    ip_addr_broadcast                        0x08013e38   Data           4  ip_addr.o(.constdata)
    tcp_persist_backoff                      0x08013e64   Data           7  tcp.o(.constdata)
    tcp_state_str                            0x08013e6c   Data          44  tcp.o(.constdata)
    tcp_backoff                              0x08013e98   Data          13  tcp.o(.constdata)
    tcp_pcb_lists                            0x08013ea8   Data          16  tcp.o(.constdata)
    memtblsize                               0x08013eb8   Data          12  malloc.o(.constdata)
    memblksize                               0x08013ec4   Data          12  malloc.o(.constdata)
    memsize                                  0x08013ed0   Data          12  malloc.o(.constdata)
    Region$$Table$$Base                      0x08014104   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08014124   Number         0  anon$$obj.o(Region$$Table)
    mem3base                                 0x10000000   Data       61440  malloc.o(.ARM.__AT_0x10000000)
    mem3mapbase                              0x1000f000   Data        3840  malloc.o(.ARM.__AT_0x1000F000)
    POINT_COLOR                              0x20000010   Data           2  lcd.o(.data)
    BACK_COLOR                               0x20000012   Data           2  lcd.o(.data)
    data_test                                0x20000014   Data           2  usart0.o(.data)
    __stdout                                 0x20000018   Data           4  usart0.o(.data)
    SystemCoreClock                          0x20000028   Data           4  system_gd32f4xx.o(.data)
    dma_current_ptp_txdesc                   0x2000002c   Data           4  gd32f4xx_enet.o(.data)
    dma_current_ptp_rxdesc                   0x20000030   Data           4  gd32f4xx_enet.o(.data)
    rxdesc_tab                               0x20000038   Data           4  gd32f4xx_enet.o(.data)
    txdesc_tab                               0x2000003c   Data           4  gd32f4xx_enet.o(.data)
    dma_current_txdesc                       0x20000040   Data           4  gd32f4xx_enet.o(.data)
    dma_current_rxdesc                       0x20000044   Data           4  gd32f4xx_enet.o(.data)
    TCPTimer                                 0x20000048   Data           4  lwip_comm.o(.data)
    ARPTimer                                 0x2000004c   Data           4  lwip_comm.o(.data)
    DHCPfineTimer                            0x20000050   Data           4  lwip_comm.o(.data)
    DHCPcoarseTimer                          0x20000054   Data           4  lwip_comm.o(.data)
    lwip_localtime                           0x2000005c   Data           4  lwip_comm.o(.data)
    current_netif                            0x20000068   Data           4  ip.o(.data)
    current_header                           0x2000006c   Data           4  ip.o(.data)
    current_iphdr_src                        0x20000070   Data           4  ip.o(.data)
    current_iphdr_dest                       0x20000074   Data           4  ip.o(.data)
    ram_heap                                 0x20000084   Data           4  mem.o(.data)
    memp_memory                              0x20000094   Data           4  memp.o(.data)
    netif_list                               0x2000009c   Data           4  netif.o(.data)
    netif_default                            0x200000a0   Data           4  netif.o(.data)
    tcp_active_pcbs_changed                  0x200000a8   Data           1  tcp.o(.data)
    tcp_ticks                                0x200000b4   Data           4  tcp.o(.data)
    tcp_bound_pcbs                           0x200000b8   Data           4  tcp.o(.data)
    tcp_listen_pcbs                          0x200000bc   Data           4  tcp.o(.data)
    tcp_active_pcbs                          0x200000c0   Data           4  tcp.o(.data)
    tcp_tw_pcbs                              0x200000c4   Data           4  tcp.o(.data)
    tcp_tmp_pcb                              0x200000c8   Data           4  tcp.o(.data)
    tcp_input_pcb                            0x200000e4   Data           4  tcp_in.o(.data)
    udp_pcbs                                 0x200000f8   Data           4  udp.o(.data)
    mallco_dev                               0x200000fc   Data          36  malloc.o(.data)
    lcddev                                   0x20000120   Data          14  lcd.o(.bss)
    rx_buff                                  0x2000016c   Data        7620  gd32f4xx_enet.o(.bss)
    tx_buff                                  0x20001f30   Data        7620  gd32f4xx_enet.o(.bss)
    lwipdev                                  0x20003cf4   Data          23  lwip_comm.o(.bss)
    lwip_netif                               0x20003d0c   Data          52  lwip_comm.o(.bss)
    dhcp_rx_options_val                      0x20003e18   Data          40  dhcp.o(.bss)
    dhcp_rx_options_given                    0x20003e40   Data          10  dhcp.o(.bss)
    mem1base                                 0x20003e84   Data       102400  malloc.o(.bss)
    mem1mapbase                              0x2001ce84   Data        6400  malloc.o(.bss)
    __initial_sp                             0x2001eb88   Data           0  startup_gd32f450_470.o(STACK)
    mem2base                                 0x68000000   Data       204800  malloc.o(.ARM.__AT_0x68000000)
    mem2mapbase                              0x68032000   Data       12800  malloc.o(.ARM.__AT_0x68032000)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00014244, Max: 0x00200000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00014124, Max: 0x00200000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         1180    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         1960  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         2240    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         2243    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         2245    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         2247    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         2248    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         2255    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         2250    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         2252    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         2241    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c4   0x080001c4   0x00000012   Code   RO            3    .text               gd32f4xx_it.o
    0x080001d6   0x080001d6   0x0000000e   Code   RO          149    .text               main.o
    0x080001e4   0x080001e4   0x000000cc   Code   RO          366    .text               systick.o
    0x080002b0   0x080002b0   0x0000003c   Code   RO          388    .text               led.o
    0x080002ec   0x080002ec   0x00000074   Code   RO          408    .text               key.o
    0x08000360   0x08000360   0x00000c4c   Code   RO          428    .text               lan8720.o
    0x08000fac   0x08000fac   0x00003c28   Code   RO          451    .text               lcd.o
    0x08004bd4   0x08004bd4   0x0000013c   Code   RO          483    .text               usart0.o
    0x08004d10   0x08004d10   0x00000abc   Code   RO          511    .text               implement.o
    0x080057cc   0x080057cc   0x00000094   Code   RO          536    .text               timer.o
    0x08005860   0x08005860   0x000001bc   Code   RO          557    .text               system_gd32f4xx.o
    0x08005a1c   0x08005a1c   0x00001062   Code   RO          744    .text               gd32f4xx_enet.o
    0x08006a7e   0x08006a7e   0x00000002   PAD
    0x08006a80   0x08006a80   0x00000790   Code   RO          773    .text               gd32f4xx_exmc.o
    0x08007210   0x08007210   0x00000196   Code   RO          854    .text               gd32f4xx_gpio.o
    0x080073a6   0x080073a6   0x00000002   PAD
    0x080073a8   0x080073a8   0x00000100   Code   RO          934    .text               gd32f4xx_misc.o
    0x080074a8   0x080074a8   0x00000680   Code   RO          977    .text               gd32f4xx_rcu.o
    0x08007b28   0x08007b28   0x000000bc   Code   RO         1059    .text               gd32f4xx_syscfg.o
    0x08007be4   0x08007be4   0x00000c62   Code   RO         1079    .text               gd32f4xx_timer.o
    0x08008846   0x08008846   0x00000002   PAD
    0x08008848   0x08008848   0x00000460   Code   RO         1140    .text               gd32f4xx_usart.o
    0x08008ca8   0x08008ca8   0x00000024   Code   RO         1181    .text               startup_gd32f450_470.o
    0x08008ccc   0x08008ccc   0x00000ad0   Code   RO         1187    .text               lwip_comm.o
    0x0800979c   0x0800979c   0x00000aa0   Code   RO         1263    .text               etharp.o
    0x0800a23c   0x0800a23c   0x000001d0   Code   RO         1359    .text               ethernetif.o
    0x0800a40c   0x0800a40c   0x00000330   Code   RO         1397    .text               icmp.o
    0x0800a73c   0x0800a73c   0x0000024c   Code   RO         1445    .text               inet_chksum.o
    0x0800a988   0x0800a988   0x0000035c   Code   RO         1460    .text               ip.o
    0x0800ace4   0x0800ace4   0x00000254   Code   RO         1497    .text               ip_addr.o
    0x0800af38   0x0800af38   0x0000082c   Code   RO         1521    .text               ip_frag.o
    0x0800b764   0x0800b764   0x00000010   Code   RO         1538    .text               def.o
    0x0800b774   0x0800b774   0x000014e4   Code   RO         1551    .text               dhcp.o
    0x0800cc58   0x0800cc58   0x0000001e   Code   RO         1581    .text               init.o
    0x0800cc76   0x0800cc76   0x00000002   PAD
    0x0800cc78   0x0800cc78   0x00000530   Code   RO         1614    .text               mem.o
    0x0800d1a8   0x0800d1a8   0x0000017c   Code   RO         1629    .text               memp.o
    0x0800d324   0x0800d324   0x0000026c   Code   RO         1658    .text               netif.o
    0x0800d590   0x0800d590   0x00000d1a   Code   RO         1681    .text               pbuf.o
    0x0800e2aa   0x0800e2aa   0x00000002   PAD
    0x0800e2ac   0x0800e2ac   0x000001f8   Code   RO         1694    .text               raw.o
    0x0800e4a4   0x0800e4a4   0x00000ff0   Code   RO         1714    .text               tcp.o
    0x0800f494   0x0800f494   0x0000111c   Code   RO         1739    .text               tcp_in.o
    0x080105b0   0x080105b0   0x00000f60   Code   RO         1762    .text               tcp_out.o
    0x08011510   0x08011510   0x0000028c   Code   RO         1775    .text               timers.o
    0x0801179c   0x0801179c   0x0000049c   Code   RO         1792    .text               udp.o
    0x08011c38   0x08011c38   0x0000000c   Code   RO         1830    .text               sys_arch.o
    0x08011c44   0x08011c44   0x000001c8   Code   RO         1928    .text               malloc.o
    0x08011e0c   0x08011e0c   0x00000024   Code   RO         1963    .text               mc_w.l(memcpya.o)
    0x08011e30   0x08011e30   0x00000024   Code   RO         1965    .text               mc_w.l(memseta.o)
    0x08011e54   0x08011e54   0x0000000e   Code   RO         1967    .text               mc_w.l(strlen.o)
    0x08011e62   0x08011e62   0x0000001a   Code   RO         1969    .text               mc_w.l(memcmp.o)
    0x08011e7c   0x08011e7c   0x000000e4   Code   RO         2234    .text               mf_w.l(dmul.o)
    0x08011f60   0x08011f60   0x0000001a   Code   RO         2236    .text               mf_w.l(dfltui.o)
    0x08011f7a   0x08011f7a   0x00000032   Code   RO         2238    .text               mf_w.l(dfixui.o)
    0x08011fac   0x08011fac   0x00000062   Code   RO         2258    .text               mc_w.l(uldiv.o)
    0x0801200e   0x0801200e   0x00000020   Code   RO         2260    .text               mc_w.l(llushr.o)
    0x0801202e   0x0801202e   0x00000000   Code   RO         2262    .text               mc_w.l(iusefp.o)
    0x0801202e   0x0801202e   0x000000ba   Code   RO         2263    .text               mf_w.l(depilogue.o)
    0x080120e8   0x080120e8   0x00000024   Code   RO         2273    .text               mc_w.l(init.o)
    0x0801210c   0x0801210c   0x0000001e   Code   RO         2275    .text               mc_w.l(llshl.o)
    0x0801212a   0x0801212a   0x00000002   PAD
    0x0801212c   0x0801212c   0x00000020   Code   RO         2180    i.__0printf$8       mc_w.l(printf8.o)
    0x0801214c   0x0801214c   0x00000028   Code   RO         2182    i.__0sprintf$8      mc_w.l(printf8.o)
    0x08012174   0x08012174   0x0000000e   Code   RO         2281    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08012182   0x08012182   0x00000002   Code   RO         2282    i.__scatterload_null  mc_w.l(handlers.o)
    0x08012184   0x08012184   0x0000000e   Code   RO         2283    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08012192   0x08012192   0x00000002   PAD
    0x08012194   0x08012194   0x00000404   Code   RO         2187    i._printf_core      mc_w.l(printf8.o)
    0x08012598   0x08012598   0x00000024   Code   RO         2188    i._printf_post_padding  mc_w.l(printf8.o)
    0x080125bc   0x080125bc   0x0000002e   Code   RO         2189    i._printf_pre_padding  mc_w.l(printf8.o)
    0x080125ea   0x080125ea   0x0000000a   Code   RO         2191    i._sputc            mc_w.l(printf8.o)
    0x080125f4   0x080125f4   0x000017c0   Data   RO          453    .constdata          lcd.o
    0x08013db4   0x08013db4   0x00000074   Data   RO          746    .constdata          gd32f4xx_enet.o
    0x08013e28   0x08013e28   0x0000000c   Data   RO         1265    .constdata          etharp.o
    0x08013e34   0x08013e34   0x00000004   Data   RO         1499    .constdata          ip_addr.o
    0x08013e38   0x08013e38   0x00000004   Data   RO         1500    .constdata          ip_addr.o
    0x08013e3c   0x08013e3c   0x00000028   Data   RO         1631    .constdata          memp.o
    0x08013e64   0x08013e64   0x00000054   Data   RO         1715    .constdata          tcp.o
    0x08013eb8   0x08013eb8   0x00000024   Data   RO         1935    .constdata          malloc.o
    0x08013edc   0x08013edc   0x00000088   Data   RO          429    .conststring        lan8720.o
    0x08013f64   0x08013f64   0x00000044   Data   RO         1266    .conststring        etharp.o
    0x08013fa8   0x08013fa8   0x0000008c   Data   RO         1553    .conststring        dhcp.o
    0x08014034   0x08014034   0x00000075   Data   RO         1716    .conststring        tcp.o
    0x080140a9   0x080140a9   0x00000003   PAD
    0x080140ac   0x080140ac   0x00000055   Data   RO         1763    .conststring        tcp_out.o
    0x08014101   0x08014101   0x00000003   PAD
    0x08014104   0x08014104   0x00000020   Data   RO         2279    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08014124, Size: 0x0001eb88, Max: 0x000b0000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08014124   0x00000008   Data   RW          367    .data               systick.o
    0x20000008   0x0801412c   0x00000008   Data   RW          430    .data               lan8720.o
    0x20000010   0x08014134   0x00000004   Data   RW          454    .data               lcd.o
    0x20000014   0x08014138   0x00000002   Data   RW          484    .data               usart0.o
    0x20000016   0x0801413a   0x00000002   PAD
    0x20000018   0x0801413c   0x00000004   Data   RW          485    .data               usart0.o
    0x2000001c   0x08014140   0x0000000c   Data   RW          513    .data               implement.o
    0x20000028   0x0801414c   0x00000004   Data   RW          558    .data               system_gd32f4xx.o
    0x2000002c   0x08014150   0x0000001c   Data   RW          747    .data               gd32f4xx_enet.o
    0x20000048   0x0801416c   0x00000018   Data   RW         1189    .data               lwip_comm.o
    0x20000060   0x08014184   0x00000001   Data   RW         1267    .data               etharp.o
    0x20000061   0x08014185   0x00000003   PAD
    0x20000064   0x08014188   0x00000014   Data   RW         1461    .data               ip.o
    0x20000078   0x0801419c   0x00000008   Data   RW         1522    .data               ip_frag.o
    0x20000080   0x080141a4   0x00000004   Data   RW         1554    .data               dhcp.o
    0x20000084   0x080141a8   0x00000010   Data   RW         1615    .data               mem.o
    0x20000094   0x080141b8   0x00000004   Data   RW         1632    .data               memp.o
    0x20000098   0x080141bc   0x0000000c   Data   RW         1659    .data               netif.o
    0x200000a4   0x080141c8   0x00000004   Data   RW         1695    .data               raw.o
    0x200000a8   0x080141cc   0x00000024   Data   RW         1717    .data               tcp.o
    0x200000cc   0x080141f0   0x0000001c   Data   RW         1741    .data               tcp_in.o
    0x200000e8   0x0801420c   0x0000000c   Data   RW         1776    .data               timers.o
    0x200000f4   0x08014218   0x00000008   Data   RW         1793    .data               udp.o
    0x200000fc   0x08014220   0x00000024   Data   RW         1936    .data               malloc.o
    0x20000120        -       0x0000000e   Zero   RW          452    .bss                lcd.o
    0x2000012e   0x08014244   0x00000002   PAD
    0x20000130        -       0x00003bc4   Zero   RW          745    .bss                gd32f4xx_enet.o
    0x20003cf4        -       0x0000004c   Zero   RW         1188    .bss                lwip_comm.o
    0x20003d40        -       0x000000c8   Zero   RW         1264    .bss                etharp.o
    0x20003e08        -       0x00000010   Zero   RW         1498    .bss                ip_addr.o
    0x20003e18        -       0x00000032   Zero   RW         1552    .bss                dhcp.o
    0x20003e4a   0x08014244   0x00000002   PAD
    0x20003e4c        -       0x00000028   Zero   RW         1630    .bss                memp.o
    0x20003e74        -       0x00000010   Zero   RW         1740    .bss                tcp_in.o
    0x20003e84        -       0x00019000   Zero   RW         1933    .bss                malloc.o
    0x2001ce84        -       0x00001900   Zero   RW         1934    .bss                malloc.o
    0x2001e784   0x08014244   0x00000004   PAD
    0x2001e788        -       0x00000400   Zero   RW         1178    STACK               startup_gd32f450_470.o



  Load Region LR$$.ARM.__AT_0x10000000 (Base: 0x10000000, Size: 0x00000000, Max: 0x0000f000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x10000000 (Exec base: 0x10000000, Load base: 0x10000000, Size: 0x0000f000, Max: 0x0000f000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x10000000        -       0x0000f000   Zero   RW         1929    .ARM.__AT_0x10000000  malloc.o



  Load Region LR$$.ARM.__AT_0x1000F000 (Base: 0x1000f000, Size: 0x00000000, Max: 0x00000f00, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x1000F000 (Exec base: 0x1000f000, Load base: 0x1000f000, Size: 0x00000f00, Max: 0x00000f00, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x1000f000        -       0x00000f00   Zero   RW         1930    .ARM.__AT_0x1000F000  malloc.o



  Load Region LR$$.ARM.__AT_0x68000000 (Base: 0x68000000, Size: 0x00000000, Max: 0x00032000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x68000000 (Exec base: 0x68000000, Load base: 0x68000000, Size: 0x00032000, Max: 0x00032000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x68000000        -       0x00032000   Zero   RW         1931    .ARM.__AT_0x68000000  malloc.o



  Load Region LR$$.ARM.__AT_0x68032000 (Base: 0x68032000, Size: 0x00000000, Max: 0x00003200, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x68032000 (Exec base: 0x68032000, Load base: 0x68032000, Size: 0x00003200, Max: 0x00003200, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x68032000        -       0x00003200   Zero   RW         1932    .ARM.__AT_0x68032000  malloc.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        16          0          0          0          0       1848   def.o
      5348       1064        140          4         50      14698   dhcp.o
      2720        590         80          1        200      28255   etharp.o
       464        122          0          0          0       2751   ethernetif.o
      4194        180        116         28      15300      62935   gd32f4xx_enet.o
      1936         16          0          0          0      13826   gd32f4xx_exmc.o
       406         10          0          0          0       4518   gd32f4xx_gpio.o
        18          0          0          0          0      89118   gd32f4xx_it.o
       256         14          0          0          0       2983   gd32f4xx_misc.o
      1664         66          0          0          0      11827   gd32f4xx_rcu.o
       188          6          0          0          0       2522   gd32f4xx_syscfg.o
      3170         32          0          0          0      19888   gd32f4xx_timer.o
      1120         16          0          0          0      12272   gd32f4xx_usart.o
       816        374          0          0          0       8581   icmp.o
      2748       1860          0         12          0       1689   implement.o
         0          0          0          0          0       4024   inet.o
       588        104          0          0          0       3801   inet_chksum.o
        30          0          0          0          0       1508   init.o
       860        158          0         20          0      17512   ip.o
       596        112          8          0         16      10503   ip_addr.o
      2092        464          0          8          0       8800   ip_frag.o
       116         10          0          0          0        916   key.o
      3148       1760        136          8          0       4463   lan8720.o
     15400        104       6080          4         14      55908   lcd.o
        60          4          0          0          0        611   led.o
      2768       1682          0         24         76      11476   lwip_comm.o
        14          0          0          0          0      88682   main.o
       456         10         36         36     391680       5955   malloc.o
      1328        504          0         16          0       4618   mem.o
       380        192         40          4         40       3616   memp.o
       620        116          0         12          0       7543   netif.o
      3354       1232          0          0          0      10247   pbuf.o
       504        132          0          4          0       4177   raw.o
        36          8        428          0       1024        992   startup_gd32f450_470.o
        12          6          0          0          0        586   sys_arch.o
       444         54          0          4          0       3029   system_gd32f4xx.o
       204         16          0          8          0       1577   systick.o
      4080        994        201         36          0      23729   tcp.o
      4380        810          0         28         16      16163   tcp_in.o
      3936        862         85          0          0      11379   tcp_out.o
       148         10          0          0          0        920   timer.o
       652        182          0         12          0       3583   timers.o
      1180        192          0          8          0      16119   udp.o
       316         16          0          6          0       3610   usart0.o

    ----------------------------------------------------------------------
     72776      <USER>       <GROUP>        288     408424     603758   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        10          0          6          5          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      1192         60          0          0          0        504   printf8.o
        14          0          0          0          0         68   strlen.o
        98          0          0          0          0         92   uldiv.o
       186          0          0          0          0        176   depilogue.o
        50          0          0          0          0         76   dfixui.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      2048         <USER>          <GROUP>          0          0       1548   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1554         76          0          0          0       1124   mc_w.l
       490          0          0          0          0        424   mf_w.l

    ----------------------------------------------------------------------
      2048         <USER>          <GROUP>          0          0       1548   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     74824      14160       7388        288     408424     595130   Grand Totals
     74824      14160       7388        288     408424     595130   ELF Image Totals
     74824      14160       7388        288          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                82212 (  80.29kB)
    Total RW  Size (RW Data + ZI Data)            408712 ( 399.13kB)
    Total ROM Size (Code + RO Data + RW Data)      82500 (  80.57kB)

==============================================================================

