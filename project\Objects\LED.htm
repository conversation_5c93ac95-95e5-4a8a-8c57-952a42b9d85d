<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\LED.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\LED.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Thu Aug 07 15:48:27 2025
<BR><P>
<H3>Maximum Stack Usage =        536 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; System_Init &rArr; lwip_comm_init &rArr; netif_add &rArr; netif_set_addr &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">MemManage_Handler</a><BR>
 <LI><a href="#[4]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">BusFault_Handler</a><BR>
 <LI><a href="#[5]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">UsageFault_Handler</a><BR>
 <LI><a href="#[6]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">SVC_Handler</a><BR>
 <LI><a href="#[7]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">DebugMon_Handler</a><BR>
 <LI><a href="#[8]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">PendSV_Handler</a><BR>
 <LI><a href="#[b1]">LCD_Fill</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[b1]">LCD_Fill</a><BR>
 <LI><a href="#[79]">Implement</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[79]">Implement</a><BR>
 <LI><a href="#[1c]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from gd32f4xx_it.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from gd32f4xx_it.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">ENET_IRQHandler</a> from lan8720.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">EXTI10_15_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[e]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from gd32f4xx_it.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[b]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from gd32f4xx_it.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from gd32f4xx_it.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from gd32f4xx_it.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[f]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[d]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from gd32f4xx_it.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from gd32f4xx_it.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">SystemInit</a> from system_gd32f4xx.o(.text) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[c]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">TIMER1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">TIMER2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">TIMER3_IRQHandler</a> from timer.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">USART0_IRQHandler</a> from usart0.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from gd32f4xx_it.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[73]">_sputc</a> from printf3.o(i._sputc) referenced from printf3.o(i.__0sprintf$3)
 <LI><a href="#[6f]">arp_timer</a> from timers.o(.text) referenced from timers.o(.text)
 <LI><a href="#[6b]">dhcp_recv</a> from dhcp.o(.text) referenced from dhcp.o(.text)
 <LI><a href="#[70]">dhcp_timer_coarse</a> from timers.o(.text) referenced from timers.o(.text)
 <LI><a href="#[71]">dhcp_timer_fine</a> from timers.o(.text) referenced from timers.o(.text)
 <LI><a href="#[68]">etharp_output</a> from etharp.o(.text) referenced from ethernetif.o(.text)
 <LI><a href="#[66]">ethernet_input</a> from etharp.o(.text) referenced from lwip_comm.o(.text)
 <LI><a href="#[67]">ethernetif_init</a> from ethernetif.o(.text) referenced from lwip_comm.o(.text)
 <LI><a href="#[72]">fputc</a> from usart0.o(.text) referenced from printf3.o(i.__0printf$3)
 <LI><a href="#[6e]">ip_reass_timer</a> from timers.o(.text) referenced from timers.o(.text)
 <LI><a href="#[6a]">ipfrag_free_pbuf_custom</a> from ip_frag.o(.text) referenced from ip_frag.o(.text)
 <LI><a href="#[69]">low_level_output</a> from ethernetif.o(.text) referenced from ethernetif.o(.text)
 <LI><a href="#[63]">main</a> from main.o(.text) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[74]">my_mem_init</a> from malloc.o(.text) referenced from malloc.o(.data)
 <LI><a href="#[75]">my_mem_perused</a> from malloc.o(.text) referenced from malloc.o(.data)
 <LI><a href="#[6c]">tcp_accept_null</a> from tcp.o(.text) referenced from tcp.o(.text)
 <LI><a href="#[19c]">tcp_recv_null</a> from tcp.o(.text) referenced from tcp.o(.text)
 <LI><a href="#[6d]">tcpip_tcp_timer</a> from timers.o(.text) referenced from timers.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[65]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[1d3]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[76]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[1ce]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[1d4]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[1d5]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[1d6]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[1d7]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[1d8]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[1d9]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>main</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 536<LI>Call Chain = main &rArr; System_Init &rArr; lwip_comm_init &rArr; netif_add &rArr; netif_set_addr &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Implement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[7a]"></a>systick_config</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, systick.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = systick_config
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_clksource_set
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[9a]"></a>delay_1us</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, systick.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ReadReg
</UL>

<P><STRONG><a name="[8e]"></a>delay_1ms</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, systick.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[7c]"></a>LED_Init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, led.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = LED_Init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[81]"></a>KEY_Init</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, key.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = KEY_Init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[82]"></a>Key_Scan</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, key.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>

<P><STRONG><a name="[84]"></a>ETH_MACDMA_Config</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, lan8720.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = ETH_MACDMA_Config &rArr; enet_init &rArr; enet_phy_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_software_reset
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_interrupt_enable
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_deinit
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
</UL>

<P><STRONG><a name="[89]"></a>LAN8720_Init</STRONG> (Thumb, 350 bytes, Stack size 16 bytes, lan8720.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = LAN8720_Init &rArr; ETH_MACDMA_Config &rArr; enet_init &rArr; enet_phy_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;syscfg_enet_phy_interface_config
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_ckout0_config
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_vector_table_set
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_MACDMA_Config
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_init
</UL>

<P><STRONG><a name="[47]"></a>ENET_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, lan8720.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = ENET_IRQHandler &rArr; lwip_pkt_handle &rArr; ethernetif_input &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_pkt_handle
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rxframe_size_get
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[120]"></a>ETH_Rx_Packet</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, lan8720.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ETH_Rx_Packet
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernetif_input
</UL>

<P><STRONG><a name="[11f]"></a>ETH_Tx_Packet</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, lan8720.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;low_level_output
</UL>

<P><STRONG><a name="[11d]"></a>ETH_GetCurrentTxBuffer</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lan8720.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;low_level_output
</UL>

<P><STRONG><a name="[95]"></a>ETH_Mem_Free</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, lan8720.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ETH_Mem_Free &rArr; myfree &rArr; my_mem_free
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myfree
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_Mem_Malloc
</UL>

<P><STRONG><a name="[97]"></a>ETH_Mem_Malloc</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lan8720.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ETH_Mem_Malloc &rArr; mymalloc &rArr; my_mem_malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymalloc
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_Mem_Free
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_init
</UL>

<P><STRONG><a name="[1da]"></a>LCD_WR_REG</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)

<P><STRONG><a name="[1db]"></a>LCD_WR_DATA</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)

<P><STRONG><a name="[9b]"></a>LCD_RD_DATA</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LCD_RD_DATA
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ReadPoint
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ReadReg
</UL>

<P><STRONG><a name="[9e]"></a>LCD_WriteReg</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lcd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Window
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fast_DrawPoint
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Scan_Dir
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayOff
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayOn
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>

<P><STRONG><a name="[99]"></a>LCD_ReadReg</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LCD_ReadReg &rArr; LCD_RD_DATA
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_RD_DATA
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1us
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[a5]"></a>LCD_WriteRAM_Prepare</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lcd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Color_Fill
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
</UL>

<P><STRONG><a name="[1dc]"></a>LCD_WriteRAM</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)

<P><STRONG><a name="[a0]"></a>LCD_BGR2RGB</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ReadPoint
</UL>

<P><STRONG><a name="[9c]"></a>opt_delay</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;opt_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;opt_delay
</UL>

<P><STRONG><a name="[9d]"></a>LCD_SetCursor</STRONG> (Thumb, 318 bytes, Stack size 36 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteReg
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Color_Fill
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Window
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ReadPoint
</UL>

<P><STRONG><a name="[9f]"></a>LCD_ReadPoint</STRONG> (Thumb, 260 bytes, Stack size 20 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BGR2RGB
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_RD_DATA
</UL>

<P><STRONG><a name="[a1]"></a>LCD_DisplayOn</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteReg
</UL>

<P><STRONG><a name="[a2]"></a>LCD_DisplayOff</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteReg
</UL>

<P><STRONG><a name="[a3]"></a>LCD_Scan_Dir</STRONG> (Thumb, 422 bytes, Stack size 20 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = LCD_Scan_Dir
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteReg
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Display_Dir
</UL>

<P><STRONG><a name="[a4]"></a>LCD_DrawPoint</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Draw_Circle
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawLine
</UL>

<P><STRONG><a name="[a6]"></a>LCD_Fast_DrawPoint</STRONG> (Thumb, 276 bytes, Stack size 36 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LCD_Fast_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteReg
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowChar
</UL>

<P><STRONG><a name="[a7]"></a>LCD_SSD_BackLightSet</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = LCD_SSD_BackLightSet &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[ab]"></a>LCD_Display_Dir</STRONG> (Thumb, 302 bytes, Stack size 36 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = LCD_Display_Dir &rArr; LCD_Scan_Dir
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Scan_Dir
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[ac]"></a>LCD_Set_Window</STRONG> (Thumb, 392 bytes, Stack size 44 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteReg
</UL>

<P><STRONG><a name="[ad]"></a>LCD_Clear</STRONG> (Thumb, 102 bytes, Stack size 28 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = LCD_Clear &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[ae]"></a>LCD_Init</STRONG> (Thumb, 11866 bytes, Stack size 176 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = LCD_Init &rArr; LCD_SSD_BackLightSet &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_init
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_enable
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Display_Dir
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SSD_BackLightSet
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ReadReg
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteReg
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_RD_DATA
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1us
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[b1]"></a>LCD_Fill</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68 + In Cycle
<LI>Call Chain = LCD_Fill &rArr;  LCD_Fill (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[b2]"></a>LCD_Color_Fill</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
</UL>

<P><STRONG><a name="[b3]"></a>LCD_DrawLine</STRONG> (Thumb, 150 bytes, Stack size 48 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawRectangle
</UL>

<P><STRONG><a name="[b4]"></a>LCD_DrawRectangle</STRONG> (Thumb, 54 bytes, Stack size 20 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawLine
</UL>

<P><STRONG><a name="[b5]"></a>LCD_Draw_Circle</STRONG> (Thumb, 176 bytes, Stack size 44 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
</UL>

<P><STRONG><a name="[b6]"></a>LCD_ShowChar</STRONG> (Thumb, 184 bytes, Stack size 40 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = LCD_ShowChar &rArr; LCD_Fast_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fast_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowxNum
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowNum
</UL>

<P><STRONG><a name="[b8]"></a>LCD_Pow</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowxNum
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowNum
</UL>

<P><STRONG><a name="[b7]"></a>LCD_ShowNum</STRONG> (Thumb, 116 bytes, Stack size 56 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Pow
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowChar
</UL>

<P><STRONG><a name="[b9]"></a>LCD_ShowxNum</STRONG> (Thumb, 160 bytes, Stack size 56 bytes, lcd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Pow
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowChar
</UL>

<P><STRONG><a name="[ba]"></a>LCD_ShowString</STRONG> (Thumb, 82 bytes, Stack size 40 bytes, lcd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = LCD_ShowString &rArr; LCD_ShowChar &rArr; LCD_Fast_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_address
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[1dd]"></a>_sys_exit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, usart0.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>fputc</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, usart0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = fputc
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf3.o(i.__0printf$3)
</UL>
<P><STRONG><a name="[bd]"></a>USART0_Config</STRONG> (Thumb, 168 bytes, Stack size 16 bytes, usart0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = USART0_Config &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_word_length_set
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_stop_bit_set
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_parity_config
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_rts_config
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_cts_config
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[c8]"></a>USART0_SendData</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, usart0.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>

<P><STRONG><a name="[2f]"></a>USART0_IRQHandler</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, usart0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = USART0_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[cb]"></a>show_address</STRONG> (Thumb, 282 bytes, Stack size 72 bytes, implement.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = show_address &rArr; LCD_ShowString &rArr; LCD_ShowChar &rArr; LCD_Fast_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[78]"></a>System_Init</STRONG> (Thumb, 244 bytes, Stack size 32 bytes, implement.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 528<LI>Call Chain = System_Init &rArr; lwip_comm_init &rArr; netif_add &rArr; netif_set_addr &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_periodic_handle
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_init
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer3_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_address
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[79]"></a>Implement</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, implement.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 472 + In Cycle
<LI>Call Chain = Implement &rArr;  Implement (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_periodic_handle
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Implement
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Implement
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cd]"></a>Timer3_Init</STRONG> (Thumb, 102 bytes, Stack size 32 bytes, timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Timer3_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_auto_reload_shadow_enable
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_timer_clock_prescaler_config
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[28]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIMER3_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>SystemInit</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, system_gd32f4xx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_240m_25m_hxtal
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_240m_25m_hxtal
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[1de]"></a>SystemCoreClockUpdate</STRONG> (Thumb, 114 bytes, Stack size 36 bytes, system_gd32f4xx.o(.text), UNUSED)

<P><STRONG><a name="[dc]"></a>enet_initpara_reset</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_deinit
</UL>

<P><STRONG><a name="[85]"></a>enet_deinit</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = enet_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_initpara_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_MACDMA_Config
</UL>

<P><STRONG><a name="[1df]"></a>enet_initpara_config</STRONG> (Thumb, 218 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[df]"></a>enet_phy_write_read</STRONG> (Thumb, 90 bytes, Stack size 20 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = enet_phy_write_read
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_init
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phyloopback_disable
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phyloopback_enable
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_config
</UL>

<P><STRONG><a name="[dd]"></a>enet_phy_config</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = enet_phy_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_write_read
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_init
</UL>

<P><STRONG><a name="[87]"></a>enet_init</STRONG> (Thumb, 748 bytes, Stack size 40 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = enet_init &rArr; enet_phy_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_config
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_write_read
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_MACDMA_Config
</UL>

<P><STRONG><a name="[86]"></a>enet_software_reset</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = enet_software_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_MACDMA_Config
</UL>

<P><STRONG><a name="[e1]"></a>enet_rxframe_drop</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rxframe_size_get
</UL>

<P><STRONG><a name="[93]"></a>enet_rxframe_size_get</STRONG> (Thumb, 68 bytes, Stack size 4 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = enet_rxframe_size_get
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rxframe_drop
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_IRQHandler
</UL>

<P><STRONG><a name="[123]"></a>enet_descriptors_chain_init</STRONG> (Thumb, 118 bytes, Stack size 20 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = enet_descriptors_chain_init
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;low_level_init
</UL>

<P><STRONG><a name="[1e0]"></a>enet_descriptors_ring_init</STRONG> (Thumb, 150 bytes, Stack size 36 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1e1]"></a>enet_frame_receive</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1e2]"></a>enet_frame_transmit</STRONG> (Thumb, 114 bytes, Stack size 12 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[124]"></a>enet_transmit_checksum_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;low_level_init
</UL>

<P><STRONG><a name="[e5]"></a>enet_rx_enable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_enable
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;low_level_init
</UL>

<P><STRONG><a name="[e3]"></a>enet_txfifo_flush</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = enet_txfifo_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_tx_disable
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_tx_enable
</UL>

<P><STRONG><a name="[e2]"></a>enet_tx_enable</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = enet_tx_enable &rArr; enet_txfifo_flush
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_txfifo_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_enable
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;low_level_init
</UL>

<P><STRONG><a name="[e4]"></a>enet_enable</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = enet_enable &rArr; enet_tx_enable &rArr; enet_txfifo_flush
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_tx_enable
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rx_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;low_level_init
</UL>

<P><STRONG><a name="[e8]"></a>enet_rx_disable</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_disable
</UL>

<P><STRONG><a name="[e6]"></a>enet_tx_disable</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, gd32f4xx_enet.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_txfifo_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_disable
</UL>

<P><STRONG><a name="[e7]"></a>enet_disable</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, gd32f4xx_enet.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_tx_disable
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rx_disable
</UL>

<P><STRONG><a name="[122]"></a>enet_mac_address_set</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;low_level_init
</UL>

<P><STRONG><a name="[1e3]"></a>enet_mac_address_get</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1e4]"></a>enet_flag_get</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1e5]"></a>enet_flag_clear</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[88]"></a>enet_interrupt_enable</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = enet_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_MACDMA_Config
</UL>

<P><STRONG><a name="[1e6]"></a>enet_interrupt_disable</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1e7]"></a>enet_interrupt_flag_get</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[94]"></a>enet_interrupt_flag_clear</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_IRQHandler
</UL>

<P><STRONG><a name="[1e8]"></a>enet_registers_get</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1e9]"></a>enet_debug_status_get</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1ea]"></a>enet_address_filter_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1eb]"></a>enet_address_filter_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1ec]"></a>enet_address_filter_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[e9]"></a>enet_phyloopback_enable</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_write_read
</UL>

<P><STRONG><a name="[ea]"></a>enet_phyloopback_disable</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_write_read
</UL>

<P><STRONG><a name="[1ed]"></a>enet_forward_feature_enable</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1ee]"></a>enet_forward_feature_disable</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1ef]"></a>enet_fliter_feature_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1f0]"></a>enet_fliter_feature_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1f1]"></a>enet_pauseframe_generate</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1f2]"></a>enet_pauseframe_detect_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1f3]"></a>enet_pauseframe_config</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1f4]"></a>enet_flowcontrol_threshold_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1f5]"></a>enet_flowcontrol_feature_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1f6]"></a>enet_flowcontrol_feature_disable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1f7]"></a>enet_dmaprocess_state_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1f8]"></a>enet_dmaprocess_resume</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1f9]"></a>enet_rxprocess_check_recovery</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1fa]"></a>enet_current_desc_address_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1fb]"></a>enet_desc_information_get</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1fc]"></a>enet_missed_frame_counter_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1fd]"></a>enet_desc_flag_get</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1fe]"></a>enet_desc_flag_set</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[1ff]"></a>enet_desc_flag_clear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[200]"></a>enet_rx_desc_immediate_receive_complete_interrupt</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[201]"></a>enet_rx_desc_delay_receive_complete_interrupt</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[202]"></a>enet_dma_feature_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[203]"></a>enet_dma_feature_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[204]"></a>enet_rx_desc_enhanced_status_get</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[205]"></a>enet_desc_select_enhanced_mode</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[206]"></a>enet_ptp_enhanced_descriptors_chain_init</STRONG> (Thumb, 108 bytes, Stack size 20 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[207]"></a>enet_ptp_enhanced_descriptors_ring_init</STRONG> (Thumb, 134 bytes, Stack size 36 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[208]"></a>enet_ptpframe_receive_enhanced_mode</STRONG> (Thumb, 178 bytes, Stack size 20 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[209]"></a>enet_ptpframe_transmit_enhanced_mode</STRONG> (Thumb, 156 bytes, Stack size 20 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[20a]"></a>enet_wum_filter_register_pointer_reset</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[20b]"></a>enet_wum_filter_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[20c]"></a>enet_wum_feature_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[20d]"></a>enet_wum_feature_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[20e]"></a>enet_msc_counters_reset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[20f]"></a>enet_msc_feature_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[210]"></a>enet_msc_feature_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[211]"></a>enet_msc_counters_preset_config</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[212]"></a>enet_msc_counters_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[213]"></a>enet_ptp_feature_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[214]"></a>enet_ptp_feature_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[215]"></a>enet_ptp_timestamp_function_config</STRONG> (Thumb, 172 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[216]"></a>enet_ptp_subsecond_increment_config</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[217]"></a>enet_ptp_timestamp_addend_config</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[218]"></a>enet_ptp_timestamp_update_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[219]"></a>enet_ptp_expected_time_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[21a]"></a>enet_ptp_system_time_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[21b]"></a>enet_ptp_pps_output_frequency_config</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_enet.o(.text), UNUSED)

<P><STRONG><a name="[21c]"></a>exmc_norsram_deinit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[21d]"></a>exmc_norsram_struct_para_init</STRONG> (Thumb, 90 bytes, Stack size 12 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[af]"></a>exmc_norsram_init</STRONG> (Thumb, 184 bytes, Stack size 20 bytes, gd32f4xx_exmc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = exmc_norsram_init
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[b0]"></a>exmc_norsram_enable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[21e]"></a>exmc_norsram_disable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[21f]"></a>exmc_nand_deinit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[220]"></a>exmc_nand_struct_para_init</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[221]"></a>exmc_nand_init</STRONG> (Thumb, 134 bytes, Stack size 12 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[222]"></a>exmc_nand_enable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[223]"></a>exmc_nand_disable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[224]"></a>exmc_pccard_deinit</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[225]"></a>exmc_pccard_struct_para_init</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[226]"></a>exmc_pccard_init</STRONG> (Thumb, 136 bytes, Stack size 8 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[227]"></a>exmc_pccard_enable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[228]"></a>exmc_pccard_disable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[229]"></a>exmc_sdram_deinit</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[22a]"></a>exmc_sdram_struct_para_init</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[22b]"></a>exmc_sdram_init</STRONG> (Thumb, 252 bytes, Stack size 36 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[22c]"></a>exmc_sqpipsram_deinit</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[22d]"></a>exmc_sqpipsram_struct_para_init</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[22e]"></a>exmc_sqpipsram_init</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[22f]"></a>exmc_norsram_consecutive_clock_config</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[230]"></a>exmc_norsram_page_size_config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[231]"></a>exmc_nand_ecc_config</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[232]"></a>exmc_ecc_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[233]"></a>exmc_sdram_readsample_enable</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[234]"></a>exmc_sdram_readsample_config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[235]"></a>exmc_sdram_command_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[236]"></a>exmc_sdram_refresh_count_set</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[237]"></a>exmc_sdram_autorefresh_number_set</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[238]"></a>exmc_sdram_write_protection_config</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[239]"></a>exmc_sdram_bankstatus_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[23a]"></a>exmc_sqpipsram_read_command_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[23b]"></a>exmc_sqpipsram_write_command_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[23c]"></a>exmc_sqpipsram_read_id_command_send</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[23d]"></a>exmc_sqpipsram_write_cmd_send</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[23e]"></a>exmc_sqpipsram_low_id_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[23f]"></a>exmc_sqpipsram_high_id_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[240]"></a>exmc_sqpipsram_send_command_state_get</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[241]"></a>exmc_interrupt_enable</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[242]"></a>exmc_interrupt_disable</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[243]"></a>exmc_flag_get</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[244]"></a>exmc_flag_clear</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[245]"></a>exmc_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[246]"></a>exmc_interrupt_flag_clear</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(.text), UNUSED)

<P><STRONG><a name="[eb]"></a>gpio_deinit</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, gd32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>

<P><STRONG><a name="[7e]"></a>gpio_mode_set</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, gd32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[7f]"></a>gpio_output_options_set</STRONG> (Thumb, 62 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[247]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[80]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[8d]"></a>gpio_bit_write</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
</UL>

<P><STRONG><a name="[248]"></a>gpio_port_write</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[83]"></a>gpio_input_bit_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Scan
</UL>

<P><STRONG><a name="[249]"></a>gpio_input_port_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[24a]"></a>gpio_output_bit_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[24b]"></a>gpio_output_port_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[8c]"></a>gpio_af_set</STRONG> (Thumb, 86 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
</UL>

<P><STRONG><a name="[24c]"></a>gpio_pin_lock</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[24d]"></a>gpio_bit_toggle</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[24e]"></a>gpio_port_toggle</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[90]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[91]"></a>nvic_irq_enable</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, gd32f4xx_misc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer3_Init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
</UL>

<P><STRONG><a name="[24f]"></a>nvic_irq_disable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_misc.o(.text), UNUSED)

<P><STRONG><a name="[8f]"></a>nvic_vector_table_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_misc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
</UL>

<P><STRONG><a name="[250]"></a>system_lowpower_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_misc.o(.text), UNUSED)

<P><STRONG><a name="[251]"></a>system_lowpower_reset</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_misc.o(.text), UNUSED)

<P><STRONG><a name="[7b]"></a>systick_clksource_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_misc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[ed]"></a>rcu_flag_get</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
</UL>

<P><STRONG><a name="[ec]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 232 bytes, Stack size 16 bytes, gd32f4xx_rcu.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_deinit
</UL>

<P><STRONG><a name="[ee]"></a>rcu_deinit</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, gd32f4xx_rcu.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
</UL>

<P><STRONG><a name="[7d]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer3_Init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_MACDMA_Config
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[252]"></a>rcu_periph_clock_disable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[253]"></a>rcu_periph_clock_sleep_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[254]"></a>rcu_periph_clock_sleep_disable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[da]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_deinit
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_deinit
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;syscfg_deinit
</UL>

<P><STRONG><a name="[db]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_deinit
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_deinit
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;syscfg_deinit
</UL>

<P><STRONG><a name="[255]"></a>rcu_bkp_reset_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[256]"></a>rcu_bkp_reset_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[257]"></a>rcu_system_clock_source_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[258]"></a>rcu_system_clock_source_get</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[259]"></a>rcu_ahb_clock_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[25a]"></a>rcu_apb1_clock_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[25b]"></a>rcu_apb2_clock_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[8a]"></a>rcu_ckout0_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
</UL>

<P><STRONG><a name="[25c]"></a>rcu_ckout1_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[25d]"></a>rcu_pll_config</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[25e]"></a>rcu_plli2s_config</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[25f]"></a>rcu_pllsai_config</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[260]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[261]"></a>rcu_rtc_div_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[262]"></a>rcu_i2s_clock_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[263]"></a>rcu_ck48m_clock_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[264]"></a>rcu_pll48m_clock_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[d1]"></a>rcu_timer_clock_prescaler_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer3_Init
</UL>

<P><STRONG><a name="[265]"></a>rcu_tli_clock_div_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[266]"></a>rcu_all_reset_flag_clear</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[267]"></a>rcu_interrupt_flag_get</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[268]"></a>rcu_interrupt_flag_clear</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[269]"></a>rcu_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[26a]"></a>rcu_interrupt_disable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[26b]"></a>rcu_lxtal_drive_capability_config</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[26c]"></a>rcu_osci_on</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[26d]"></a>rcu_osci_off</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[26e]"></a>rcu_osci_bypass_mode_enable</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[26f]"></a>rcu_osci_bypass_mode_disable</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[270]"></a>rcu_hxtal_clock_monitor_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[271]"></a>rcu_hxtal_clock_monitor_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[272]"></a>rcu_irc16m_adjust_value_set</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[273]"></a>rcu_voltage_key_unlock</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[274]"></a>rcu_deepsleep_voltage_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[275]"></a>rcu_spread_spectrum_config</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[276]"></a>rcu_spread_spectrum_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[277]"></a>rcu_spread_spectrum_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(.text), UNUSED)

<P><STRONG><a name="[de]"></a>rcu_clock_freq_get</STRONG> (Thumb, 194 bytes, Stack size 52 bytes, gd32f4xx_rcu.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_config
</UL>

<P><STRONG><a name="[ef]"></a>syscfg_deinit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, gd32f4xx_syscfg.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>

<P><STRONG><a name="[278]"></a>syscfg_bootmode_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_syscfg.o(.text), UNUSED)

<P><STRONG><a name="[279]"></a>syscfg_fmc_swap_config</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_syscfg.o(.text), UNUSED)

<P><STRONG><a name="[27a]"></a>syscfg_exmc_swap_config</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_syscfg.o(.text), UNUSED)

<P><STRONG><a name="[27b]"></a>syscfg_exti_line_config</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, gd32f4xx_syscfg.o(.text), UNUSED)

<P><STRONG><a name="[8b]"></a>syscfg_enet_phy_interface_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_syscfg.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
</UL>

<P><STRONG><a name="[27c]"></a>syscfg_compensation_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_syscfg.o(.text), UNUSED)

<P><STRONG><a name="[27d]"></a>syscfg_flag_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_syscfg.o(.text), UNUSED)

<P><STRONG><a name="[d2]"></a>timer_deinit</STRONG> (Thumb, 190 bytes, Stack size 8 bytes, gd32f4xx_timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer3_Init
</UL>

<P><STRONG><a name="[27e]"></a>timer_struct_para_init</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[d3]"></a>timer_init</STRONG> (Thumb, 122 bytes, Stack size 12 bytes, gd32f4xx_timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = timer_init
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer3_Init
</UL>

<P><STRONG><a name="[d7]"></a>timer_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer3_Init
</UL>

<P><STRONG><a name="[27f]"></a>timer_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[d4]"></a>timer_auto_reload_shadow_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer3_Init
</UL>

<P><STRONG><a name="[280]"></a>timer_auto_reload_shadow_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[281]"></a>timer_update_event_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[282]"></a>timer_update_event_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[283]"></a>timer_counter_alignment</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[284]"></a>timer_counter_up_direction</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[285]"></a>timer_counter_down_direction</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[286]"></a>timer_prescaler_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[287]"></a>timer_repetition_value_config</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[288]"></a>timer_autoreload_value_config</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[289]"></a>timer_counter_value_config</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[28a]"></a>timer_counter_read</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[28b]"></a>timer_prescaler_read</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[28c]"></a>timer_single_pulse_mode_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[28d]"></a>timer_update_source_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[28e]"></a>timer_dma_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[28f]"></a>timer_dma_disable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[290]"></a>timer_channel_dma_request_source_select</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[291]"></a>timer_dma_transfer_config</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[292]"></a>timer_event_software_generate</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[293]"></a>timer_break_struct_para_init</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[294]"></a>timer_break_config</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[295]"></a>timer_break_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[296]"></a>timer_break_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[297]"></a>timer_automatic_output_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[298]"></a>timer_automatic_output_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[299]"></a>timer_primary_output_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[29a]"></a>timer_channel_control_shadow_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[29b]"></a>timer_channel_control_shadow_update_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[29c]"></a>timer_channel_output_struct_para_init</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[29d]"></a>timer_channel_output_config</STRONG> (Thumb, 490 bytes, Stack size 12 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[29e]"></a>timer_channel_output_mode_config</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[29f]"></a>timer_channel_output_pulse_value_config</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2a0]"></a>timer_channel_output_shadow_config</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2a1]"></a>timer_channel_output_fast_config</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2a2]"></a>timer_channel_output_clear_config</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2a3]"></a>timer_channel_output_polarity_config</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2a4]"></a>timer_channel_complementary_output_polarity_config</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2a5]"></a>timer_channel_output_state_config</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2a6]"></a>timer_channel_complementary_output_state_config</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2a7]"></a>timer_channel_input_struct_para_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[f1]"></a>timer_channel_input_capture_prescaler_config</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_input_pwm_capture_config
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_input_capture_config
</UL>

<P><STRONG><a name="[f0]"></a>timer_input_capture_config</STRONG> (Thumb, 298 bytes, Stack size 4 bytes, gd32f4xx_timer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_input_capture_prescaler_config
</UL>

<P><STRONG><a name="[2a8]"></a>timer_channel_capture_value_register_read</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[f2]"></a>timer_input_pwm_capture_config</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, gd32f4xx_timer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_input_capture_prescaler_config
</UL>

<P><STRONG><a name="[2a9]"></a>timer_hall_mode_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[f4]"></a>timer_input_trigger_source_select</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_external_trigger_as_external_clock_config
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_internal_trigger_as_external_clock_config
</UL>

<P><STRONG><a name="[2aa]"></a>timer_master_output_trigger_source_select</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2ab]"></a>timer_slave_mode_select</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2ac]"></a>timer_master_slave_mode_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[f7]"></a>timer_external_trigger_config</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, gd32f4xx_timer.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_external_clock_mode1_config
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_external_clock_mode0_config
</UL>

<P><STRONG><a name="[2ad]"></a>timer_quadrature_decoder_mode_config</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2ae]"></a>timer_internal_clock_config</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[f3]"></a>timer_internal_trigger_as_external_clock_config</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, gd32f4xx_timer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_input_trigger_source_select
</UL>

<P><STRONG><a name="[f5]"></a>timer_external_trigger_as_external_clock_config</STRONG> (Thumb, 156 bytes, Stack size 8 bytes, gd32f4xx_timer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_input_trigger_source_select
</UL>

<P><STRONG><a name="[f6]"></a>timer_external_clock_mode0_config</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_timer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_external_trigger_config
</UL>

<P><STRONG><a name="[f8]"></a>timer_external_clock_mode1_config</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, gd32f4xx_timer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_external_trigger_config
</UL>

<P><STRONG><a name="[2af]"></a>timer_external_clock_mode1_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2b0]"></a>timer_channel_remap_config</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2b1]"></a>timer_write_chxval_register_config</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2b2]"></a>timer_output_value_selection_config</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2b3]"></a>timer_flag_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[2b4]"></a>timer_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[d6]"></a>timer_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer3_Init
</UL>

<P><STRONG><a name="[2b5]"></a>timer_interrupt_disable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text), UNUSED)

<P><STRONG><a name="[d8]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_IRQHandler
</UL>

<P><STRONG><a name="[d5]"></a>timer_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_IRQHandler
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer3_Init
</UL>

<P><STRONG><a name="[be]"></a>usart_deinit</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
</UL>

<P><STRONG><a name="[c2]"></a>usart_baudrate_set</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
</UL>

<P><STRONG><a name="[c1]"></a>usart_parity_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
</UL>

<P><STRONG><a name="[bf]"></a>usart_word_length_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
</UL>

<P><STRONG><a name="[c0]"></a>usart_stop_bit_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
</UL>

<P><STRONG><a name="[c7]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
</UL>

<P><STRONG><a name="[2b6]"></a>usart_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[c4]"></a>usart_transmit_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
</UL>

<P><STRONG><a name="[c3]"></a>usart_receive_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
</UL>

<P><STRONG><a name="[2b7]"></a>usart_data_first_config</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2b8]"></a>usart_invert_config</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2b9]"></a>usart_oversample_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2ba]"></a>usart_sample_bit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2bb]"></a>usart_receiver_timeout_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2bc]"></a>usart_receiver_timeout_disable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2bd]"></a>usart_receiver_timeout_threshold_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[bb]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_SendData
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[ca]"></a>usart_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[2be]"></a>usart_address_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2bf]"></a>usart_mute_mode_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2c0]"></a>usart_mute_mode_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2c1]"></a>usart_mute_mode_wakeup_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2c2]"></a>usart_lin_mode_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2c3]"></a>usart_lin_mode_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2c4]"></a>usart_lin_break_detection_length_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2c5]"></a>usart_send_break</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2c6]"></a>usart_halfduplex_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2c7]"></a>usart_halfduplex_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2c8]"></a>usart_synchronous_clock_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2c9]"></a>usart_synchronous_clock_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2ca]"></a>usart_synchronous_clock_config</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2cb]"></a>usart_guard_time_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2cc]"></a>usart_smartcard_mode_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2cd]"></a>usart_smartcard_mode_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2ce]"></a>usart_smartcard_mode_nack_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2cf]"></a>usart_smartcard_mode_nack_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2d0]"></a>usart_smartcard_autoretry_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2d1]"></a>usart_block_length_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2d2]"></a>usart_irda_mode_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2d3]"></a>usart_irda_mode_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2d4]"></a>usart_prescaler_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2d5]"></a>usart_irda_lowpower_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[c5]"></a>usart_hardware_flow_rts_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
</UL>

<P><STRONG><a name="[c6]"></a>usart_hardware_flow_cts_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Config
</UL>

<P><STRONG><a name="[2d6]"></a>usart_break_frame_coherence_config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2d7]"></a>usart_parity_check_coherence_config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2d8]"></a>usart_hardware_flow_coherence_config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2d9]"></a>usart_dma_receive_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2da]"></a>usart_dma_transmit_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[bc]"></a>usart_flag_get</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_SendData
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[2db]"></a>usart_flag_clear</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2dc]"></a>usart_interrupt_enable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[2dd]"></a>usart_interrupt_disable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[c9]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 48 bytes, Stack size 12 bytes, gd32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[2de]"></a>usart_interrupt_flag_clear</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f9]"></a>lwip_comm_mem_free</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, lwip_comm.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = lwip_comm_mem_free &rArr; myfree &rArr; my_mem_free
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myfree
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_mem_malloc
</UL>

<P><STRONG><a name="[fa]"></a>lwip_comm_mem_malloc</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, lwip_comm.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = lwip_comm_mem_malloc &rArr; mymalloc &rArr; my_mem_malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymalloc
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_get_memorysize
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_mem_free
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_init
</UL>

<P><STRONG><a name="[fd]"></a>lwip_comm_default_ip_set</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, lwip_comm.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lwip_comm_default_ip_set
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_init
</UL>

<P><STRONG><a name="[cf]"></a>lwip_comm_init</STRONG> (Thumb, 112 bytes, Stack size 40 bytes, lwip_comm.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 496<LI>Call Chain = lwip_comm_init &rArr; netif_add &rArr; netif_set_addr &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_Mem_Malloc
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LAN8720_Init
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_up
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_default
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_add
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_default_ip_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_mem_malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[92]"></a>lwip_pkt_handle</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lwip_comm.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = lwip_pkt_handle &rArr; ethernetif_input &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernetif_input
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_IRQHandler
</UL>

<P><STRONG><a name="[103]"></a>lwip_dhcp_process_handle</STRONG> (Thumb, 318 bytes, Stack size 32 bytes, lwip_comm.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = lwip_dhcp_process_handle &rArr; dhcp_start &rArr; dhcp_discover &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_periodic_handle
</UL>

<P><STRONG><a name="[d0]"></a>lwip_periodic_handle</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, lwip_comm.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = lwip_periodic_handle &rArr; dhcp_fine_tmr &rArr; dhcp_timeout &rArr; dhcp_bind &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_tmr
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_tmr
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_fine_tmr
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_coarse_tmr
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_dhcp_process_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Implement
</UL>

<P><STRONG><a name="[105]"></a>etharp_tmr</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = etharp_tmr &rArr; etharp_free_entry &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_free_entry
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_periodic_handle
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arp_timer
</UL>

<P><STRONG><a name="[10e]"></a>etharp_cleanup_netif</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = etharp_cleanup_netif &rArr; etharp_free_entry &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_free_entry
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_down
</UL>

<P><STRONG><a name="[10f]"></a>etharp_find_addr</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, etharp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_find_entry
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[115]"></a>etharp_request</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = etharp_request &rArr; etharp_raw &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_raw
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_output_to_arp_index
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_up
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_link_up
</UL>

<P><STRONG><a name="[117]"></a>etharp_query</STRONG> (Thumb, 298 bytes, Stack size 56 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = etharp_query &rArr; etharp_request &rArr; etharp_raw &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_ref
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_copy
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip4_addr_isbroadcast
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_request
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_send_ip
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_find_entry
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_output
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_check
</UL>

<P><STRONG><a name="[68]"></a>etharp_output</STRONG> (Thumb, 306 bytes, Stack size 32 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = etharp_output &rArr; etharp_query &rArr; etharp_request &rArr; etharp_raw &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip4_addr_isbroadcast
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_output_to_arp_index
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_send_ip
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ethernetif.o(.text)
</UL>
<P><STRONG><a name="[66]"></a>ethernet_input</STRONG> (Thumb, 380 bytes, Stack size 16 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 536<LI>Call Chain = ethernet_input &rArr; ip_input &rArr; tcp_input &rArr; tcp_listen_input &rArr; tcp_alloc &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_arp_input
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lwip_comm.o(.text)
</UL>
<P><STRONG><a name="[102]"></a>ethernetif_input</STRONG> (Thumb, 116 bytes, Stack size 40 bytes, ethernetif.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = ethernetif_input &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_Rx_Packet
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_pkt_handle
</UL>

<P><STRONG><a name="[67]"></a>ethernetif_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, ethernetif.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = ethernetif_init &rArr; low_level_init &rArr; enet_descriptors_chain_init
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;low_level_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lwip_comm.o(.text)
</UL>
<P><STRONG><a name="[125]"></a>icmp_input</STRONG> (Thumb, 310 bytes, Stack size 48 bytes, icmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = icmp_input &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output_if
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum_pbuf
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_copy
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip4_addr_isbroadcast
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
</UL>

<P><STRONG><a name="[12b]"></a>icmp_dest_unreach</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, icmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = icmp_dest_unreach &rArr; icmp_send_response &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_send_response
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_input
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
</UL>

<P><STRONG><a name="[12c]"></a>icmp_time_exceeded</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, icmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = icmp_time_exceeded &rArr; icmp_send_response &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_send_response
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_free_complete_datagram
</UL>

<P><STRONG><a name="[12d]"></a>inet_chksum_pseudo</STRONG> (Thumb, 136 bytes, Stack size 32 bytes, inet_chksum.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_standard_chksum
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
</UL>

<P><STRONG><a name="[12f]"></a>inet_chksum_pseudo_partial</STRONG> (Thumb, 178 bytes, Stack size 40 bytes, inet_chksum.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_standard_chksum
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[129]"></a>inet_chksum</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, inet_chksum.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = inet_chksum &rArr; lwip_standard_chksum
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_standard_chksum
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_send_response
</UL>

<P><STRONG><a name="[126]"></a>inet_chksum_pbuf</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, inet_chksum.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = inet_chksum_pbuf &rArr; lwip_standard_chksum
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_standard_chksum
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_input
</UL>

<P><STRONG><a name="[137]"></a>ip_route</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, ip.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ip_route
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_segment
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_connect
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_eff_send_mss
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_sendto
</UL>

<P><STRONG><a name="[11c]"></a>ip_input</STRONG> (Thumb, 366 bytes, Stack size 40 bytes, ip.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 520<LI>Call Chain = ip_input &rArr; tcp_input &rArr; tcp_listen_input &rArr; tcp_alloc &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_input
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_input
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_dest_unreach
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_input
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip4_addr_isbroadcast
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernet_input
</UL>

<P><STRONG><a name="[127]"></a>ip_output_if</STRONG> (Thumb, 210 bytes, Stack size 40 bytes, ip.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_input
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_sendto
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
</UL>

<P><STRONG><a name="[12a]"></a>ip_output</STRONG> (Thumb, 72 bytes, Stack size 40 bytes, ip.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_route
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output_if
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_send_response
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_segment
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_send_empty_ack
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_zero_window_probe
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_keepalive
</UL>

<P><STRONG><a name="[10d]"></a>ip4_addr_isbroadcast</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, ip_addr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_input
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_input
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_output
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_update_arp_entry
</UL>

<P><STRONG><a name="[138]"></a>ip4_addr_netmask_valid</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, ip_addr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
</UL>

<P><STRONG><a name="[13a]"></a>ipaddr_aton</STRONG> (Thumb, 262 bytes, Stack size 40 bytes, ip_addr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ipaddr_addr
</UL>

<P><STRONG><a name="[13b]"></a>ipaddr_addr</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ip_addr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ipaddr_aton
</UL>

<P><STRONG><a name="[13d]"></a>ipaddr_ntoa_r</STRONG> (Thumb, 118 bytes, Stack size 36 bytes, ip_addr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ipaddr_ntoa
</UL>

<P><STRONG><a name="[13c]"></a>ipaddr_ntoa</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ip_addr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ipaddr_ntoa_r
</UL>

<P><STRONG><a name="[142]"></a>ip_reass_tmr</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ip_frag.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = ip_reass_tmr &rArr; ip_reass_free_complete_datagram &rArr; icmp_time_exceeded &rArr; icmp_send_response &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_free_complete_datagram
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_timer
</UL>

<P><STRONG><a name="[132]"></a>ip_reass</STRONG> (Thumb, 824 bytes, Stack size 40 bytes, ip_frag.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = ip_reass &rArr; ip_reass_remove_oldest_datagram &rArr; ip_reass_free_complete_datagram &rArr; icmp_time_exceeded &rArr; icmp_send_response &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_clen
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_cat
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_malloc
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_chain_frag_into_datagram_and_validate
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_remove_oldest_datagram
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_dequeue_datagram
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
</UL>

<P><STRONG><a name="[136]"></a>ip_frag</STRONG> (Thumb, 382 bytes, Stack size 88 bytes, ip_frag.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_cat
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloced_custom
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_malloc
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag_free_pbuf_custom_ref
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_ref
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output_if
</UL>

<P><STRONG><a name="[111]"></a>lwip_htons</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, def.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum_pseudo_partial
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum_pseudo
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output_if
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_raw
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_arp_input
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_segment
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_create_segment
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_alloc_header
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_zero_window_probe
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_send_fin
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
</UL>

<P><STRONG><a name="[130]"></a>lwip_ntohs</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, def.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_chain_frag_into_datagram_and_validate
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_input
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_parseopt
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_zero_window_probe
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_send_fin
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
</UL>

<P><STRONG><a name="[139]"></a>lwip_htonl</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, def.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ipaddr_aton
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip4_addr_netmask_valid
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_segment
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_create_segment
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_alloc_header
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_send_empty_ack
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_keepalive
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_handle_ack
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_parse_reply
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
</UL>

<P><STRONG><a name="[156]"></a>lwip_ntohl</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, def.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_segment
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rexmit
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_decline
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_parse_reply
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
</UL>

<P><STRONG><a name="[157]"></a>dhcp_renew</STRONG> (Thumb, 132 bytes, Stack size 24 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = dhcp_renew &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_delete_msg
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_short
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_trailer
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_coarse_tmr
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timeout
</UL>

<P><STRONG><a name="[107]"></a>dhcp_coarse_tmr</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = dhcp_coarse_tmr &rArr; dhcp_renew &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_renew
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_rebind
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_periodic_handle
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timer_coarse
</UL>

<P><STRONG><a name="[15a]"></a>dhcp_release</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = dhcp_release &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_delete_msg
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_netmask
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_ipaddr
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_gw
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_down
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_trailer
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timeout
</UL>

<P><STRONG><a name="[106]"></a>dhcp_fine_tmr</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = dhcp_fine_tmr &rArr; dhcp_timeout &rArr; dhcp_bind &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timeout
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_periodic_handle
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timer_fine
</UL>

<P><STRONG><a name="[161]"></a>dhcp_set_struct</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, dhcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>

<P><STRONG><a name="[162]"></a>dhcp_cleanup</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, dhcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_free
</UL>

<P><STRONG><a name="[164]"></a>dhcp_stop</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = dhcp_stop &rArr; udp_remove &rArr; memp_free &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_remove
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
</UL>

<P><STRONG><a name="[ff]"></a>dhcp_start</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = dhcp_start &rArr; dhcp_discover &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_remove
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_recv
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_new
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_connect
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_bind
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_malloc
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_stop
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_init
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_dhcp_process_handle
</UL>

<P><STRONG><a name="[16e]"></a>dhcp_inform</STRONG> (Thumb, 168 bytes, Stack size 80 bytes, dhcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_delete_msg
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_remove
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_new
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_bind
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_short
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_trailer
</UL>

<P><STRONG><a name="[16f]"></a>dhcp_network_changed</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, dhcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_down
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_link_up
</UL>

<P><STRONG><a name="[112]"></a>dhcp_arp_reply</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = dhcp_arp_reply &rArr; dhcp_decline &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_decline
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_arp_input
</UL>

<P><STRONG><a name="[fc]"></a>lwip_init</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, init.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = lwip_init &rArr; sys_timeouts_init &rArr; sys_timeout &rArr; memp_malloc &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_init
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_init
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeouts_init
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_init
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_init
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_init
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_init
</UL>

<P><STRONG><a name="[171]"></a>mem_init</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, mem.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = mem_init
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_init
</UL>

<P><STRONG><a name="[163]"></a>mem_free</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, mem.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;plug_holes
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_cleanup
</UL>

<P><STRONG><a name="[178]"></a>mem_trim</STRONG> (Thumb, 240 bytes, Stack size 32 bytes, mem.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = mem_trim &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
</UL>

<P><STRONG><a name="[16d]"></a>mem_malloc</STRONG> (Thumb, 252 bytes, Stack size 32 bytes, mem.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = mem_malloc &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_calloc
</UL>

<P><STRONG><a name="[179]"></a>mem_calloc</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, mem.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_malloc
</UL>

<P><STRONG><a name="[fb]"></a>memp_get_memorysize</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, memp.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_mem_malloc
</UL>

<P><STRONG><a name="[172]"></a>memp_init</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, memp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = memp_init
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_init
</UL>

<P><STRONG><a name="[145]"></a>memp_malloc</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, memp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = memp_malloc &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeout
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_create_segment
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_alloc
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_with_backlog
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_new
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_new
</UL>

<P><STRONG><a name="[13f]"></a>memp_free</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, memp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = memp_free &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag_free_pbuf_custom_ref
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_dequeue_datagram
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_check_timeouts
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_untimeout
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_with_backlog
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abandon
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_slowtmr
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_seg_free
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close_shutdown
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_remove
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_remove
</UL>

<P><STRONG><a name="[173]"></a>netif_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, netif.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_init
</UL>

<P><STRONG><a name="[15d]"></a>netif_set_gw</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, netif.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_addr
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_bind
</UL>

<P><STRONG><a name="[15e]"></a>netif_set_netmask</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, netif.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_addr
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_bind
</UL>

<P><STRONG><a name="[15c]"></a>netif_set_ipaddr</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, netif.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abort
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_addr
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_bind
</UL>

<P><STRONG><a name="[17c]"></a>netif_set_addr</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, netif.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = netif_set_addr &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_netmask
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_ipaddr
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_gw
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_add
</UL>

<P><STRONG><a name="[fe]"></a>netif_add</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, netif.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = netif_add &rArr; netif_set_addr &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_init
</UL>

<P><STRONG><a name="[100]"></a>netif_set_default</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, netif.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_init
</UL>

<P><STRONG><a name="[15b]"></a>netif_set_down</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, netif.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = netif_set_down &rArr; etharp_cleanup_netif &rArr; etharp_free_entry &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_cleanup_netif
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_remove
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_network_changed
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
</UL>

<P><STRONG><a name="[17d]"></a>netif_remove</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, netif.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_down
</UL>

<P><STRONG><a name="[2df]"></a>netif_find</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, netif.o(.text), UNUSED)

<P><STRONG><a name="[101]"></a>netif_set_up</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, netif.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = netif_set_up &rArr; etharp_request &rArr; etharp_raw &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_request
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_init
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_bind
</UL>

<P><STRONG><a name="[17e]"></a>netif_set_link_up</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, netif.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_request
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_network_changed
</UL>

<P><STRONG><a name="[2e0]"></a>netif_set_link_down</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, netif.o(.text), UNUSED)

<P><STRONG><a name="[109]"></a>pbuf_free</STRONG> (Thumb, 170 bytes, Stack size 16 bytes, pbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_free
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_delete_msg
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ipfrag_free_pbuf_custom
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_chain_frag_into_datagram_and_validate
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_free_complete_datagram
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_input
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_input
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_send_response
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_raw
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_arp_input
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_update_arp_entry
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_free_entry
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernetif_input
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernet_input
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_create_segment
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_send_empty_ack
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_zero_window_probe
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_keepalive
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_shutdown
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_recv_null
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_purge
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_seg_free
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_sendto
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_coalesce
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_dechain
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
</UL>

<P><STRONG><a name="[114]"></a>pbuf_alloc</STRONG> (Thumb, 460 bytes, Stack size 40 bytes, pbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_malloc
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_input
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_send_response
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_raw
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernetif_input
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pbuf_prealloc
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_alloc_header
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_enqueue_flags
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_sendto
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_coalesce
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
</UL>

<P><STRONG><a name="[149]"></a>pbuf_alloced_custom</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, pbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = pbuf_alloced_custom &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
</UL>

<P><STRONG><a name="[131]"></a>pbuf_realloc</STRONG> (Thumb, 186 bytes, Stack size 32 bytes, pbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = pbuf_realloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_trim
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_renew
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_decline
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_rebind
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>

<P><STRONG><a name="[11a]"></a>pbuf_header</STRONG> (Thumb, 806 bytes, Stack size 16 bytes, pbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = pbuf_header &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_input
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output_if
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_input
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_output
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernet_input
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_create_segment
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_sendto
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
</UL>

<P><STRONG><a name="[141]"></a>pbuf_clen</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, pbuf.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_chain_frag_into_datagram_and_validate
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_free_complete_datagram
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_enqueue_flags
</UL>

<P><STRONG><a name="[119]"></a>pbuf_ref</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, pbuf.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_chain
</UL>

<P><STRONG><a name="[147]"></a>pbuf_cat</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, pbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = pbuf_cat &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_chain
</UL>

<P><STRONG><a name="[17f]"></a>pbuf_chain</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, pbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = pbuf_chain &rArr; pbuf_cat &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_cat
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_ref
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_sendto
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
</UL>

<P><STRONG><a name="[180]"></a>pbuf_dechain</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, pbuf.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[118]"></a>pbuf_copy</STRONG> (Thumb, 258 bytes, Stack size 32 bytes, pbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = pbuf_copy &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_input
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_coalesce
</UL>

<P><STRONG><a name="[167]"></a>pbuf_copy_partial</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, pbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = pbuf_copy_partial &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_zero_window_probe
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_parse_reply
</UL>

<P><STRONG><a name="[181]"></a>pbuf_take</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, pbuf.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>

<P><STRONG><a name="[182]"></a>pbuf_coalesce</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, pbuf.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_copy
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[184]"></a>pbuf_get_at</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, pbuf.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_memcmp
</UL>

<P><STRONG><a name="[183]"></a>pbuf_memcmp</STRONG> (Thumb, 646 bytes, Stack size 20 bytes, pbuf.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_get_at
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_memfind
</UL>

<P><STRONG><a name="[185]"></a>pbuf_memfind</STRONG> (Thumb, 56 bytes, Stack size 20 bytes, pbuf.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_strstr
</UL>

<P><STRONG><a name="[186]"></a>pbuf_strstr</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, pbuf.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_memfind
</UL>

<P><STRONG><a name="[133]"></a>raw_input</STRONG> (Thumb, 104 bytes, Stack size 32 bytes, raw.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = raw_input
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
</UL>

<P><STRONG><a name="[2e1]"></a>raw_bind</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, raw.o(.text), UNUSED)

<P><STRONG><a name="[2e2]"></a>raw_connect</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, raw.o(.text), UNUSED)

<P><STRONG><a name="[2e3]"></a>raw_recv</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, raw.o(.text), UNUSED)

<P><STRONG><a name="[188]"></a>raw_sendto</STRONG> (Thumb, 160 bytes, Stack size 40 bytes, raw.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_route
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output_if
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_chain
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_send
</UL>

<P><STRONG><a name="[189]"></a>raw_send</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, raw.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_sendto
</UL>

<P><STRONG><a name="[18a]"></a>raw_remove</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, raw.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
</UL>

<P><STRONG><a name="[18b]"></a>raw_new</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, raw.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_malloc
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>

<P><STRONG><a name="[175]"></a>tcp_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, tcp.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_init
</UL>

<P><STRONG><a name="[18c]"></a>tcp_seg_free</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = tcp_seg_free &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
</UL>
<BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_create_segment
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_segs_free
</UL>

<P><STRONG><a name="[18d]"></a>tcp_segs_free</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = tcp_segs_free &rArr; tcp_seg_free &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_seg_free
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abandon
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_purge
</UL>

<P><STRONG><a name="[18e]"></a>tcp_pcb_purge</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = tcp_pcb_purge &rArr; tcp_segs_free &rArr; tcp_seg_free &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_segs_free
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_remove
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_slowtmr
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close_shutdown
</UL>

<P><STRONG><a name="[18f]"></a>tcp_slowtmr</STRONG> (Thumb, 754 bytes, Stack size 40 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = tcp_slowtmr &rArr; tcp_rexmit_rto &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_zero_window_probe
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rexmit_rto
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_keepalive
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_purge
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_tmr
</UL>

<P><STRONG><a name="[195]"></a>tcp_pcb_remove</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_purge
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abandon
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close_shutdown
</UL>

<P><STRONG><a name="[199]"></a>tcp_close</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = tcp_close &rArr; tcp_close_shutdown &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close_shutdown
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_recv_null
</UL>

<P><STRONG><a name="[19a]"></a>tcp_update_rcv_ann_wnd</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = tcp_update_rcv_ann_wnd &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_recved
</UL>

<P><STRONG><a name="[19b]"></a>tcp_recved</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = tcp_recved &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_update_rcv_ann_wnd
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_recv_null
</UL>

<P><STRONG><a name="[19c]"></a>tcp_recv_null</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = tcp_recv_null &rArr; tcp_close &rArr; tcp_close_shutdown &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_recved
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process_refused_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> tcp.o(.text)
</UL>
<P><STRONG><a name="[19d]"></a>tcp_process_refused_data</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = tcp_process_refused_data &rArr; tcp_recv_null &rArr; tcp_close &rArr; tcp_close_shutdown &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_recv_null
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_fasttmr
</UL>

<P><STRONG><a name="[19e]"></a>tcp_fasttmr</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = tcp_fasttmr &rArr; tcp_process_refused_data &rArr; tcp_recv_null &rArr; tcp_close &rArr; tcp_close_shutdown &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process_refused_data
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_tmr
</UL>

<P><STRONG><a name="[104]"></a>tcp_tmr</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = tcp_tmr &rArr; tcp_fasttmr &rArr; tcp_process_refused_data &rArr; tcp_recv_null &rArr; tcp_close &rArr; tcp_close_shutdown &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_fasttmr
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_slowtmr
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_periodic_handle
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcpip_tcp_timer
</UL>

<P><STRONG><a name="[19f]"></a>tcp_shutdown</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, tcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close_shutdown
</UL>

<P><STRONG><a name="[1a0]"></a>tcp_abandon</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_remove
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_segs_free
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_input
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abort
</UL>

<P><STRONG><a name="[17b]"></a>tcp_abort</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abandon
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_alloc
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_ipaddr
</UL>

<P><STRONG><a name="[1a1]"></a>tcp_bind</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, tcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_timer_needed
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_new_port
</UL>

<P><STRONG><a name="[1a3]"></a>tcp_listen_with_backlog</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, tcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_malloc
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_timer_needed
</UL>

<P><STRONG><a name="[1a4]"></a>tcp_eff_send_mss</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = tcp_eff_send_mss &rArr; ip_route
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_route
</UL>
<BR>[Called By]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_segment
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_input
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_connect
</UL>

<P><STRONG><a name="[1a6]"></a>tcp_next_iss</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tcp.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_alloc
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_connect
</UL>

<P><STRONG><a name="[1a5]"></a>tcp_connect</STRONG> (Thumb, 266 bytes, Stack size 32 bytes, tcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_route
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_timer_needed
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_enqueue_flags
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_next_iss
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_eff_send_mss
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_new_port
</UL>

<P><STRONG><a name="[2e4]"></a>tcp_setprio</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, tcp.o(.text), UNUSED)

<P><STRONG><a name="[1a8]"></a>tcp_alloc</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = tcp_alloc &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_malloc
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_next_iss
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abort
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_input
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_new
</UL>

<P><STRONG><a name="[1a9]"></a>tcp_new</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, tcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_alloc
</UL>

<P><STRONG><a name="[2e5]"></a>tcp_arg</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, tcp.o(.text), UNUSED)

<P><STRONG><a name="[1aa]"></a>tcp_recv</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, tcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[1ab]"></a>tcp_sent</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, tcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[1ac]"></a>tcp_err</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, tcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[2e6]"></a>tcp_accept</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, tcp.o(.text), UNUSED)

<P><STRONG><a name="[1ad]"></a>tcp_poll</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, tcp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[2e7]"></a>tcp_debug_state_str</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, tcp.o(.text), UNUSED)

<P><STRONG><a name="[135]"></a>tcp_input</STRONG> (Thumb, 1212 bytes, Stack size 40 bytes, tcp_in.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 480<LI>Call Chain = tcp_input &rArr; tcp_listen_input &rArr; tcp_alloc &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip4_addr_isbroadcast
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_timewait_input
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_input
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process_refused_data
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_recv_null
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_remove
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abort
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
</UL>

<P><STRONG><a name="[1a7]"></a>tcp_enqueue_flags</STRONG> (Thumb, 286 bytes, Stack size 32 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = tcp_enqueue_flags &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_clen
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_create_segment
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_input
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_send_fin
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_connect
</UL>

<P><STRONG><a name="[198]"></a>tcp_send_fin</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = tcp_send_fin &rArr; tcp_enqueue_flags &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_enqueue_flags
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close_shutdown
</UL>

<P><STRONG><a name="[1ba]"></a>tcp_write</STRONG> (Thumb, 1410 bytes, Stack size 96 bytes, tcp_out.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_clen
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_cat
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write_checks
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pbuf_prealloc
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_create_segment
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_segs_free
</UL>

<P><STRONG><a name="[1b0]"></a>tcp_send_empty_ack</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = tcp_send_empty_ack &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_alloc_header
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
</UL>

<P><STRONG><a name="[194]"></a>tcp_output</STRONG> (Thumb, 440 bytes, Stack size 32 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_segment
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_send_empty_ack
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_seg_free
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_timewait_input
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_input
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rexmit_rto
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_connect
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_fasttmr
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_recved
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_remove
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_slowtmr
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close_shutdown
</UL>

<P><STRONG><a name="[193]"></a>tcp_rst</STRONG> (Thumb, 144 bytes, Stack size 48 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = tcp_rst &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_timewait_input
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_input
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abandon
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_slowtmr
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close_shutdown
</UL>

<P><STRONG><a name="[191]"></a>tcp_rexmit_rto</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = tcp_rexmit_rto &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_slowtmr
</UL>

<P><STRONG><a name="[1b3]"></a>tcp_rexmit</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tcp_rexmit
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rexmit_fast
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
</UL>

<P><STRONG><a name="[1af]"></a>tcp_rexmit_fast</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = tcp_rexmit_fast &rArr; tcp_rexmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rexmit
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
</UL>

<P><STRONG><a name="[192]"></a>tcp_keepalive</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = tcp_keepalive &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_alloc_header
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_slowtmr
</UL>

<P><STRONG><a name="[190]"></a>tcp_zero_window_probe</STRONG> (Thumb, 220 bytes, Stack size 32 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = tcp_zero_window_probe &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_alloc_header
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_copy_partial
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_slowtmr
</UL>

<P><STRONG><a name="[1bc]"></a>sys_timeout</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, timers.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sys_timeout &rArr; memp_malloc &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_malloc
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timer_fine
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timer_coarse
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arp_timer
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_timer
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcpip_tcp_timer
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_timer_needed
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeouts_init
</UL>

<P><STRONG><a name="[197]"></a>tcp_timer_needed</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, timers.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = tcp_timer_needed &rArr; sys_timeout &rArr; memp_malloc &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeout
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_input
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_connect
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_with_backlog
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_bind
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close_shutdown
</UL>

<P><STRONG><a name="[176]"></a>sys_timeouts_init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, timers.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sys_timeouts_init &rArr; sys_timeout &rArr; memp_malloc &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_now
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeout
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_init
</UL>

<P><STRONG><a name="[1be]"></a>sys_untimeout</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, timers.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
</UL>

<P><STRONG><a name="[1bf]"></a>sys_check_timeouts</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, timers.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_now
</UL>

<P><STRONG><a name="[1c0]"></a>sys_restart_timeouts</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, timers.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_now
</UL>

<P><STRONG><a name="[174]"></a>udp_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, udp.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_init
</UL>

<P><STRONG><a name="[134]"></a>udp_input</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, udp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = udp_input &rArr; icmp_dest_unreach &rArr; icmp_send_response &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_dest_unreach
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip4_addr_isbroadcast
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_input
</UL>

<P><STRONG><a name="[16a]"></a>udp_bind</STRONG> (Thumb, 192 bytes, Stack size 40 bytes, udp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = udp_bind &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_connect
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
</UL>

<P><STRONG><a name="[152]"></a>udp_sendto_if</STRONG> (Thumb, 202 bytes, Stack size 48 bytes, udp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output_if
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_chain
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_bind
</UL>
<BR>[Called By]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_renew
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_decline
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_rebind
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>

<P><STRONG><a name="[1c1]"></a>udp_sendto</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, udp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_route
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_send
</UL>

<P><STRONG><a name="[1c2]"></a>udp_send</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, udp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto
</UL>

<P><STRONG><a name="[16b]"></a>udp_connect</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, udp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = udp_connect &rArr; udp_bind &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_bind
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
</UL>

<P><STRONG><a name="[2e8]"></a>udp_disconnect</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, udp.o(.text), UNUSED)

<P><STRONG><a name="[16c]"></a>udp_recv</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, udp.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
</UL>

<P><STRONG><a name="[165]"></a>udp_remove</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, udp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = udp_remove &rArr; memp_free &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_stop
</UL>

<P><STRONG><a name="[169]"></a>udp_new</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, udp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = udp_new &rArr; memp_malloc &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_malloc
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
</UL>

<P><STRONG><a name="[1bd]"></a>sys_now</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sys_arch.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_restart_timeouts
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_check_timeouts
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeouts_init
</UL>

<P><STRONG><a name="[1c7]"></a>mymemcpy</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, malloc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myrealloc
</UL>

<P><STRONG><a name="[1c3]"></a>mymemset</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, malloc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_init
</UL>

<P><STRONG><a name="[74]"></a>my_mem_init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, malloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = my_mem_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymemset
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> malloc.o(.data)
</UL>
<P><STRONG><a name="[75]"></a>my_mem_perused</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, malloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = my_mem_perused
</UL>
<BR>[Address Reference Count : 1]<UL><LI> malloc.o(.data)
</UL>
<P><STRONG><a name="[1c5]"></a>my_mem_malloc</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, malloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = my_mem_malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymalloc
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myrealloc
</UL>

<P><STRONG><a name="[1c4]"></a>my_mem_free</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, malloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = my_mem_free
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myfree
</UL>

<P><STRONG><a name="[96]"></a>myfree</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, malloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = myfree &rArr; my_mem_free
</UL>
<BR>[Calls]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_free
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_Mem_Free
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_mem_free
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myrealloc
</UL>

<P><STRONG><a name="[98]"></a>mymalloc</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, malloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = mymalloc &rArr; my_mem_malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_Mem_Malloc
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_comm_mem_malloc
</UL>

<P><STRONG><a name="[1c6]"></a>myrealloc</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, malloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myfree
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_malloc
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymemcpy
</UL>

<P><STRONG><a name="[11e]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_free_complete_datagram
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_send_response
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;low_level_output
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_copy
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernetif_input
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_take
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_copy_partial
</UL>

<P><STRONG><a name="[2e9]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[2ea]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[1c8]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[2eb]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[2ec]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[17a]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_calloc
</UL>

<P><STRONG><a name="[146]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_alloc
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_new
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_new
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_struct
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_parse_reply
</UL>

<P><STRONG><a name="[2ed]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[1c9]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[187]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_strstr
</UL>

<P><STRONG><a name="[11b]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernet_input
</UL>

<P><STRONG><a name="[a9]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SSD_BackLightSet
</UL>

<P><STRONG><a name="[a8]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SSD_BackLightSet
</UL>

<P><STRONG><a name="[aa]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SSD_BackLightSet
</UL>

<P><STRONG><a name="[2ee]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[1d2]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1cb]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[2ef]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[2f0]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[1cd]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[1ca]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>

<P><STRONG><a name="[77]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[2f1]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[1cc]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[2f2]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[1cf]"></a>__0printf$3</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printf3.o(i.__0printf$3), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2f3]"></a>__1printf$3</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf3.o(i.__0printf$3), UNUSED)

<P><STRONG><a name="[ce]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf3.o(i.__0printf$3))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_delete_msg
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_cat
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloced_custom
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_malloc
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ipfrag_free_pbuf_custom
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag_free_pbuf_custom_ref
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_chain_frag_into_datagram_and_validate
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_free_complete_datagram
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_dequeue_datagram
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ipaddr_aton
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_input
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum_pseudo_partial
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output_if
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_input
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_send_response
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_copy
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_arp_reply
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_output
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_find_addr
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_output_to_arp_index
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_raw
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_arp_input
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_update_arp_entry
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_send_ip
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_find_entry
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_add
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernetif_init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernet_input
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_dhcp_process_handle
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeout
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write_checks
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pbuf_prealloc
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output_alloc_header
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_enqueue_flags
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_poll
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_err
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_sent
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_recv
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_connect
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_with_backlog
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_bind
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abandon
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_recved
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_update_rcv_ann_wnd
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_remove
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_slowtmr
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close_shutdown
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;raw_sendto
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_coalesce
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_take
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_dechain
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_trim
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;plug_holes
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_bind
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_copy_partial
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_malloc
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_free
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_stop
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_cleanup
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_struct
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_parse_reply
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_bind
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_long
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_short
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_byte
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_trailer
</UL>

<P><STRONG><a name="[1d1]"></a>__0sprintf$3</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printf3.o(i.__0sprintf$3), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2f4]"></a>__1sprintf$3</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf3.o(i.__0sprintf$3), UNUSED)

<P><STRONG><a name="[cc]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf3.o(i.__0sprintf$3))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_address
</UL>

<P><STRONG><a name="[2f5]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[2f6]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[2f7]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[d9]"></a>system_clock_240m_25m_hxtal</STRONG> (Thumb, 156 bytes, Stack size 8 bytes, system_gd32f4xx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_240m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[e0]"></a>enet_delay</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, gd32f4xx_enet.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = enet_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_config
</UL>

<P><STRONG><a name="[108]"></a>etharp_free_entry</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = etharp_free_entry &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_cleanup_netif
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_find_entry
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_tmr
</UL>

<P><STRONG><a name="[10a]"></a>etharp_find_entry</STRONG> (Thumb, 332 bytes, Stack size 64 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = etharp_find_entry &rArr; etharp_free_entry &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_free_entry
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_find_addr
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_update_arp_entry
</UL>

<P><STRONG><a name="[10b]"></a>etharp_send_ip</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = etharp_send_ip &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_output
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_output_to_arp_index
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_update_arp_entry
</UL>

<P><STRONG><a name="[10c]"></a>etharp_update_arp_entry</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = etharp_update_arp_entry &rArr; etharp_find_entry &rArr; etharp_free_entry &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip4_addr_isbroadcast
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_send_ip
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_find_entry
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_arp_input
</UL>

<P><STRONG><a name="[110]"></a>etharp_arp_input</STRONG> (Thumb, 590 bytes, Stack size 40 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = etharp_arp_input &rArr; dhcp_arp_reply &rArr; dhcp_decline &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_arp_reply
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_update_arp_entry
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernet_input
</UL>

<P><STRONG><a name="[113]"></a>etharp_raw</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = etharp_raw &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_request
</UL>

<P><STRONG><a name="[116]"></a>etharp_output_to_arp_index</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, etharp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = etharp_output_to_arp_index &rArr; etharp_request &rArr; etharp_raw &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_request
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_send_ip
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_output
</UL>

<P><STRONG><a name="[69]"></a>low_level_output</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, ethernetif.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = low_level_output
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_GetCurrentTxBuffer
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ETH_Tx_Packet
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ethernetif.o(.text)
</UL>
<P><STRONG><a name="[121]"></a>low_level_init</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, ethernetif.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = low_level_init &rArr; enet_descriptors_chain_init
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_mac_address_set
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_enable
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_tx_enable
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rx_enable
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_transmit_checksum_config
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_descriptors_chain_init
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernetif_init
</UL>

<P><STRONG><a name="[128]"></a>icmp_send_response</STRONG> (Thumb, 120 bytes, Stack size 40 bytes, icmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = icmp_send_response &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_time_exceeded
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_dest_unreach
</UL>

<P><STRONG><a name="[12e]"></a>lwip_standard_chksum</STRONG> (Thumb, 86 bytes, Stack size 12 bytes, inet_chksum.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lwip_standard_chksum
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum_pseudo_partial
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum_pseudo
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum_pbuf
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inet_chksum
</UL>

<P><STRONG><a name="[13e]"></a>ip_reass_dequeue_datagram</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, ip_frag.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ip_reass_dequeue_datagram &rArr; memp_free &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_free_complete_datagram
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
</UL>

<P><STRONG><a name="[140]"></a>ip_reass_free_complete_datagram</STRONG> (Thumb, 202 bytes, Stack size 32 bytes, ip_frag.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = ip_reass_free_complete_datagram &rArr; icmp_time_exceeded &rArr; icmp_send_response &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_clen
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_dequeue_datagram
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;icmp_time_exceeded
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_tmr
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_remove_oldest_datagram
</UL>

<P><STRONG><a name="[143]"></a>ip_reass_remove_oldest_datagram</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, ip_frag.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = ip_reass_remove_oldest_datagram &rArr; ip_reass_free_complete_datagram &rArr; icmp_time_exceeded &rArr; icmp_send_response &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_free_complete_datagram
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
</UL>

<P><STRONG><a name="[144]"></a>ip_reass_chain_frag_into_datagram_and_validate</STRONG> (Thumb, 344 bytes, Stack size 32 bytes, ip_frag.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = ip_reass_chain_frag_into_datagram_and_validate &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_clen
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass
</UL>

<P><STRONG><a name="[148]"></a>ip_frag_free_pbuf_custom_ref</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, ip_frag.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ip_frag_free_pbuf_custom_ref &rArr; memp_free &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ipfrag_free_pbuf_custom
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag
</UL>

<P><STRONG><a name="[6a]"></a>ipfrag_free_pbuf_custom</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, ip_frag.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = ipfrag_free_pbuf_custom &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_frag_free_pbuf_custom_ref
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ip_frag.o(.text)
</UL>
<P><STRONG><a name="[14a]"></a>dhcp_delete_msg</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = dhcp_delete_msg &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_renew
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_decline
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_rebind
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>

<P><STRONG><a name="[14b]"></a>dhcp_option_trailer</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = dhcp_option_trailer &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_renew
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_decline
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_rebind
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>

<P><STRONG><a name="[14c]"></a>dhcp_option_byte</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = dhcp_option_byte &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
</UL>

<P><STRONG><a name="[14d]"></a>dhcp_option</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = dhcp_option &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_renew
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_decline
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_rebind
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
</UL>

<P><STRONG><a name="[14e]"></a>dhcp_option_short</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = dhcp_option_short &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_renew
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_rebind
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>

<P><STRONG><a name="[14f]"></a>dhcp_create_msg</STRONG> (Thumb, 372 bytes, Stack size 24 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = dhcp_create_msg &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_renew
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_decline
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_rebind
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>

<P><STRONG><a name="[150]"></a>dhcp_discover</STRONG> (Thumb, 810 bytes, Stack size 24 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = dhcp_discover &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_delete_msg
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_short
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_byte
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_trailer
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_start
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_network_changed
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timeout
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
</UL>

<P><STRONG><a name="[153]"></a>dhcp_check</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = dhcp_check &rArr; etharp_query &rArr; etharp_request &rArr; etharp_raw &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_query
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timeout
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
</UL>

<P><STRONG><a name="[154]"></a>dhcp_option_long</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = dhcp_option_long &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_decline
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
</UL>

<P><STRONG><a name="[155]"></a>dhcp_select</STRONG> (Thumb, 228 bytes, Stack size 24 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = dhcp_select &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_delete_msg
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_long
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_short
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_byte
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_trailer
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timeout
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
</UL>

<P><STRONG><a name="[158]"></a>dhcp_rebind</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = dhcp_rebind &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_delete_msg
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_short
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_trailer
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_coarse_tmr
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timeout
</UL>

<P><STRONG><a name="[159]"></a>dhcp_reboot</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = dhcp_reboot &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_delete_msg
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_long
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_short
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_trailer
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_network_changed
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timeout
</UL>

<P><STRONG><a name="[15f]"></a>dhcp_bind</STRONG> (Thumb, 308 bytes, Stack size 32 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = dhcp_bind &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_up
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_netmask
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_ipaddr
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_gw
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_timeout
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
</UL>

<P><STRONG><a name="[166]"></a>dhcp_parse_reply</STRONG> (Thumb, 1000 bytes, Stack size 56 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = dhcp_parse_reply &rArr; pbuf_copy_partial &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_copy_partial
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
</UL>

<P><STRONG><a name="[6b]"></a>dhcp_recv</STRONG> (Thumb, 290 bytes, Stack size 24 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = dhcp_recv &rArr; dhcp_bind &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_netmask
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_ipaddr
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_gw
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;netif_set_down
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_handle_ack
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_parse_reply
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_bind
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_check
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>
<BR>[Address Reference Count : 1]<UL><LI> dhcp.o(.text)
</UL>
<P><STRONG><a name="[151]"></a>dhcp_set_state</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, dhcp.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_inform
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_stop
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_renew
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_decline
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_bind
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_rebind
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_check
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>

<P><STRONG><a name="[160]"></a>dhcp_timeout</STRONG> (Thumb, 196 bytes, Stack size 8 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = dhcp_timeout &rArr; dhcp_bind &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_release
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_renew
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_bind
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_reboot
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_rebind
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_select
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_check
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_discover
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_fine_tmr
</UL>

<P><STRONG><a name="[168]"></a>dhcp_handle_ack</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dhcp_handle_ack
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_recv
</UL>

<P><STRONG><a name="[170]"></a>dhcp_decline</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, dhcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = dhcp_decline &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_delete_msg
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;udp_sendto_if
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_set_state
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_long
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_create_msg
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_option_trailer
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_arp_reply
</UL>

<P><STRONG><a name="[177]"></a>plug_holes</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, mem.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_free
</UL>

<P><STRONG><a name="[196]"></a>tcp_close_shutdown</STRONG> (Thumb, 814 bytes, Stack size 40 bytes, tcp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = tcp_close_shutdown &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_free
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_timer_needed
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_send_fin
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_remove
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_purge
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_shutdown
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_close
</UL>

<P><STRONG><a name="[1a2]"></a>tcp_new_port</STRONG> (Thumb, 276 bytes, Stack size 20 bytes, tcp.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_connect
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_bind
</UL>

<P><STRONG><a name="[6c]"></a>tcp_accept_null</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tcp.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> tcp.o(.text)
</UL>
<P><STRONG><a name="[1ae]"></a>tcp_receive</STRONG> (Thumb, 1584 bytes, Stack size 32 bytes, tcp_in.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = tcp_receive &rArr; tcp_send_empty_ack &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_clen
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_realloc
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_send_empty_ack
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rexmit_fast
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_update_rcv_ann_wnd
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_seg_free
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
</UL>

<P><STRONG><a name="[1b1]"></a>tcp_parseopt</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, tcp_in.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = tcp_parseopt
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohs
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_listen_input
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_process
</UL>

<P><STRONG><a name="[1b2]"></a>tcp_process</STRONG> (Thumb, 1108 bytes, Stack size 48 bytes, tcp_in.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = tcp_process &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rexmit
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_parseopt
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_receive
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_timer_needed
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_eff_send_mss
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_pcb_purge
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_seg_free
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abort
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
</UL>

<P><STRONG><a name="[1b4]"></a>tcp_listen_input</STRONG> (Thumb, 208 bytes, Stack size 24 bytes, tcp_in.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = tcp_listen_input &rArr; tcp_alloc &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_parseopt
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_timer_needed
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_enqueue_flags
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_alloc
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_eff_send_mss
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_abandon
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
</UL>

<P><STRONG><a name="[1b5]"></a>tcp_timewait_input</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, tcp_in.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = tcp_timewait_input &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_rst
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_input
</UL>

<P><STRONG><a name="[1b6]"></a>tcp_output_alloc_header</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = tcp_output_alloc_header &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_send_empty_ack
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_zero_window_probe
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_keepalive
</UL>

<P><STRONG><a name="[1b7]"></a>tcp_create_segment</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = tcp_create_segment &rArr; tcp_seg_free &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memp_malloc
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_header
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_free
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_seg_free
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_enqueue_flags
</UL>

<P><STRONG><a name="[1b8]"></a>tcp_pbuf_prealloc</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, tcp_out.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pbuf_alloc
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
</UL>

<P><STRONG><a name="[1b9]"></a>tcp_write_checks</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, tcp_out.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_write
</UL>

<P><STRONG><a name="[1bb]"></a>tcp_output_segment</STRONG> (Thumb, 528 bytes, Stack size 24 bytes, tcp_out.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_ntohl
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htonl
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_route
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_output
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lwip_htons
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_eff_send_mss
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_output
</UL>

<P><STRONG><a name="[6d]"></a>tcpip_tcp_timer</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, timers.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = tcpip_tcp_timer &rArr; tcp_tmr &rArr; tcp_fasttmr &rArr; tcp_process_refused_data &rArr; tcp_recv_null &rArr; tcp_close &rArr; tcp_close_shutdown &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_tmr
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeout
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(.text)
</UL>
<P><STRONG><a name="[6e]"></a>ip_reass_timer</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, timers.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = ip_reass_timer &rArr; ip_reass_tmr &rArr; ip_reass_free_complete_datagram &rArr; icmp_time_exceeded &rArr; icmp_send_response &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ip_reass_tmr
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeout
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(.text)
</UL>
<P><STRONG><a name="[6f]"></a>arp_timer</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, timers.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = arp_timer &rArr; etharp_tmr &rArr; etharp_free_entry &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;etharp_tmr
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeout
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(.text)
</UL>
<P><STRONG><a name="[70]"></a>dhcp_timer_coarse</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, timers.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = dhcp_timer_coarse &rArr; dhcp_coarse_tmr &rArr; dhcp_renew &rArr; udp_sendto_if &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_coarse_tmr
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeout
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(.text)
</UL>
<P><STRONG><a name="[71]"></a>dhcp_timer_fine</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, timers.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = dhcp_timer_fine &rArr; dhcp_fine_tmr &rArr; dhcp_timeout &rArr; dhcp_bind &rArr; netif_set_ipaddr &rArr; tcp_abort &rArr; tcp_abandon &rArr; tcp_pcb_remove &rArr; tcp_output &rArr; tcp_output_segment &rArr; ip_output &rArr; ip_output_if &rArr; ip_frag &rArr; pbuf_alloc &rArr; pbuf_free &rArr; mem_free &rArr; plug_holes &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dhcp_fine_tmr
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_timeout
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(.text)
</UL>
<P><STRONG><a name="[1d0]"></a>_printf_core</STRONG> (Thumb, 436 bytes, Stack size 96 bytes, printf3.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf$3
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf$3
</UL>

<P><STRONG><a name="[73]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printf3.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf$3
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf3.o(i.__0sprintf$3)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
